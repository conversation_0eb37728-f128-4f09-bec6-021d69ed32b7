package com.chic.dea.apis.controller;

import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.dea.apis.model.dto.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 数据源类型控制器
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@RestController
@RequestMapping("/api/datasource-types")
public class DataSourceTypeController {

    /**
     * 获取所有支持的数据源类型
     * 
     * @return 数据源类型列表
     */
    @GetMapping
    public Result<List<Map<String, Object>>> getDataSourceTypes() {
        try {
            List<Map<String, Object>> types = new ArrayList<>();
            
            for (DataSourceType type : DataSourceType.values()) {
                Map<String, Object> typeInfo = new HashMap<>();
                typeInfo.put("code", type.getCode());
                typeInfo.put("name", type.getName());
                typeInfo.put("driverClassName", type.getDriverClassName());
                typeInfo.put("defaultPort", type.getDefaultPort());
                types.add(typeInfo);
            }
            
            return Result.success(types);
        } catch (Exception e) {
            log.error("获取数据源类型列表失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "获取数据源类型列表失败: " + e.getMessage()));
        }
    }
}