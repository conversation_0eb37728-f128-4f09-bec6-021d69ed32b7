package com.chic.dea.infrastructure.general.util;

import com.chic.dea.apis.model.dto.DataSourceType;
import com.chic.dea.apis.model.dto.TestConnectionResponse;
import com.chic.dea.domain.database.entity.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据源连接工具类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
public class DataSourceConnectionUtil {

    /**
     * 连接超时时间(秒)
     */
    private static final int CONNECTION_TIMEOUT = 30;

    /**
     * 测试数据源连接
     * 
     * @param dataSource 数据源实体
     * @return 测试连接响应
     */
    public static TestConnectionResponse testConnection(DataSource dataSource) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 验证数据源参数
            validateDataSource(dataSource);
            
            // 获取数据源类型
            DataSourceType sourceType = DataSourceType.fromCode(dataSource.getType());
            
            // 构建JDBC URL
            String jdbcUrl = sourceType.buildJdbcUrl(dataSource.getHost(), dataSource.getPort(), dataSource.getDatabaseName());
            
            // 加载数据库驱动
            Class.forName(sourceType.getDriverClassName());
            
            // 设置连接超时
            DriverManager.setLoginTimeout(CONNECTION_TIMEOUT);
            
            // 尝试连接数据库
            try (Connection connection = DriverManager.getConnection(jdbcUrl, dataSource.getUsername(), dataSource.getPassword())) {
                // 获取数据库元信息
                DatabaseMetaData metaData = connection.getMetaData();
                String databaseProductName = metaData.getDatabaseProductName();
                String databaseVersion = metaData.getDatabaseProductVersion();
                
                long duration = System.currentTimeMillis() - startTime;
                
                log.info("数据源连接测试成功: {}, 耗时: {}ms", dataSource.getName(), duration);
                return TestConnectionResponse.success(duration, databaseVersion, databaseProductName);
            }
            
        } catch (ClassNotFoundException e) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMessage = "数据库驱动未找到: " + e.getMessage();
            log.error("数据源连接测试失败: {}, 错误: {}", dataSource.getName(), errorMessage);
            return TestConnectionResponse.failure(errorMessage, duration);
            
        } catch (SQLException e) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMessage = "数据库连接失败: " + e.getMessage();
            log.error("数据源连接测试失败: {}, 错误: {}", dataSource.getName(), errorMessage);
            return TestConnectionResponse.failure(errorMessage, duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMessage = "连接测试异常: " + e.getMessage();
            log.error("数据源连接测试失败: {}, 错误: {}", dataSource.getName(), errorMessage);
            return TestConnectionResponse.failure(errorMessage, duration);
        }
    }

    /**
     * 获取数据库连接
     * 
     * @param dataSource 数据源实体
     * @return 数据库连接
     * @throws SQLException SQL异常
     */
    public static Connection getConnection(DataSource dataSource) throws SQLException {
        try {
            // 验证数据源参数
            validateDataSource(dataSource);
            
            // 获取数据源类型
            DataSourceType sourceType = DataSourceType.fromCode(dataSource.getType());
            
            // 构建JDBC URL
            String jdbcUrl = sourceType.buildJdbcUrl(dataSource.getHost(), dataSource.getPort(), dataSource.getDatabaseName());
            
            // 加载数据库驱动
            Class.forName(sourceType.getDriverClassName());
            
            // 获取连接
            return DriverManager.getConnection(jdbcUrl, dataSource.getUsername(), dataSource.getPassword());
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("数据库驱动未找到: " + e.getMessage(), e);
        }
    }

    /**
     * 验证数据源参数
     * 
     * @param dataSource 数据源实体
     */
    private static void validateDataSource(DataSource dataSource) {
        if (dataSource == null) {
            throw new IllegalArgumentException("数据源不能为空");
        }
        
        if (!StringUtils.hasText(dataSource.getType())) {
            throw new IllegalArgumentException("数据源类型不能为空");
        }
        
        if (!StringUtils.hasText(dataSource.getHost())) {
            throw new IllegalArgumentException("主机地址不能为空");
        }
        
        if (dataSource.getPort() == null || dataSource.getPort() <= 0) {
            throw new IllegalArgumentException("端口号不能为空且必须大于0");
        }
        
        if (!StringUtils.hasText(dataSource.getDatabaseName())) {
            throw new IllegalArgumentException("数据库名不能为空");
        }
        
        if (!StringUtils.hasText(dataSource.getUsername())) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        
        if (!StringUtils.hasText(dataSource.getPassword())) {
            throw new IllegalArgumentException("密码不能为空");
        }
    }

    /**
     * 关闭数据库连接
     * 
     * @param connection 数据库连接
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                log.warn("关闭数据库连接时发生异常: {}", e.getMessage());
            }
        }
    }
}