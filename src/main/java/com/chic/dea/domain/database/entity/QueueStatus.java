package com.chic.dea.domain.database.entity;

/**
 * 任务队列状态枚举
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public enum QueueStatus {
    
    /**
     * 待执行
     */
    PENDING("待执行"),
    
    /**
     * 执行中
     */
    PROCESSING("执行中"),
    
    /**
     * 已完成
     */
    COMPLETED("已完成"),
    
    /**
     * 执行失败
     */
    FAILED("执行失败");

    private final String description;

    QueueStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 检查状态是否为最终状态
     * 
     * @return true if final status
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED;
    }
    
    /**
     * 检查状态是否可以重试
     * 
     * @return true if retryable
     */
    public boolean isRetryable() {
        return this == FAILED;
    }
}