<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chic</groupId>
        <artifactId>chic-parent</artifactId>
        <version>2.0.1</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.chic</groupId>
    <artifactId>data-extract-audit</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>data-extract-audit</name>
    <packaging>jar</packaging>
    <description>data extract audit project for Spring Boot</description>

    <properties>
        <java.version>1.8</java.version>
        <druid.version>1.2.11</druid.version>
        <mybatis.plus.version>3.4.2</mybatis.plus.version>
        <dynamic.version>3.4.1</dynamic.version>
        <oracle.client.version>********</oracle.client.version>
        <pagehelper.version>1.4.1</pagehelper.version>
        <redisson.version>3.13.6</redisson.version>
        <mybatis-generator-maven-plugin.version>1.3.7</mybatis-generator-maven-plugin.version>
        <maven-release-plugin.version>3.0.0-M7</maven-release-plugin.version>
        <mybatis-generator-plugin.verison>1.3.10</mybatis-generator-plugin.verison>
        <mysql-connector-java.version>8.0.31</mysql-connector-java.version>
        <mybatis-plus-generator.version>3.4.1</mybatis-plus-generator.version>
        <guava.version>31.1-jre</guava.version>
        <httpclient.version>4.5.13</httpclient.version>
        <dom4j.version>2.1.3</dom4j.version>
        <version.lombok>1.18.24</version.lombok>
        <reflectasm.version>1.11.9</reflectasm.version>
        <okhttp.version>4.9.0</okhttp.version>
        <minio.version>1.1</minio.version>
        <maven.version>2.2.13.RELEASE</maven.version>
        <mapstruct.verison>1.5.3.Final</mapstruct.verison>
        <easyexcel.version>3.2.1</easyexcel.version>
        <hutool.version>5.8.19</hutool.version>
        <commons.version>1.0.6</commons.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <skyauth.version>1.4</skyauth.version>
        <calcite.version>1.38.0</calcite.version>
    </properties>

    <scm>
        <tag>HEAD</tag>
        <connection>scm:git:http://gitlab.chinahuanong.com.cn/bigdata/data-extract-audit.git</connection>
        <developerConnection>scm:git:******************************:bigdata/data-extract-audit.git</developerConnection>
        <url>http://gitlab.chinahuanong.com.cn/bigdata/data-extract-audit.git</url>
    </scm>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <version>${version.lombok}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>${oracle.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.nls</groupId>
            <artifactId>orai18n</artifactId>
            <version>${oracle.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis.plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${dynamic.version}</version>
        </dependency>
        <!-- redis server  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- lettuce pool 缓存连接池-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
        </dependency>
        <!-- 自动生成代码工具 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus-generator.version}</version>
            <scope>compile</scope>
        </dependency>

        <!-- guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <!-- httpclient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>${httpclient.version}</version>
        </dependency>
        <!-- xml解析处理 -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>${dom4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chic.commons</groupId>
            <artifactId>commons-util</artifactId>
            <version>${commons.version}</version>
        </dependency>
        <!--包含mapstruct所需的注解-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.verison}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.verison}</version>
        </dependency>
        <!-- excel导出 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <artifactId>mysql-connector-j</artifactId>
            <groupId>com.mysql</groupId>
            <version>${mysql-connector-java.version}</version>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>1.10.5</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.1-jre</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.ulisesbocchio/jasypt-spring-boot-starter -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>3.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.chic.skyauth</groupId>
            <artifactId>skyauth-client-http</artifactId>
            <version>${skyauth.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>8.4.1.jre8</version>
        </dependency>
        <dependency>
            <groupId>org.apache.calcite</groupId>
            <artifactId>calcite-core</artifactId>
            <version>${calcite.version}</version>
        </dependency>

        <!-- OkHttp 用于调用外部血缘服务 -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>


    </dependencies>
    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <arguments>-DskipTests</arguments>
                </configuration>
                <version>${maven-release-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>${mybatis-generator-maven-plugin.version}</version>
                <configuration>
                    <configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.oracle.database.jdbc</groupId>
                        <artifactId>ojdbc8</artifactId>
                        <version>${oracle.client.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.oracle.database.nls</groupId>
                        <artifactId>orai18n</artifactId>
                        <version>${oracle.client.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql-connector-java.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>


    </build>
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://maven.chinahuanong.com.cn/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://maven.chinahuanong.com.cn/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
