package com.chic.dea.domain.database.entity;

/**
 * 提数任务状态枚举
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public enum ExtractionStatus {
    
    /**
     * 草稿
     */
    DRAFT("草稿"),
    
    /**
     * 待审批
     */
    PENDING_APPROVAL("待审批"),
    
    /**
     * 已审批
     */
    APPROVED("已审批"),
    
    /**
     * 执行中
     */
    EXECUTING("执行中"),
    
    /**
     * 已完成
     */
    COMPLETED("已完成"),
    
    /**
     * 执行失败
     */
    FAILED("执行失败"),
    
    /**
     * 已归档
     */
    ARCHIVED("已归档");

    private final String description;

    ExtractionStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 检查状态是否允许编辑
     * 
     * @return true if editable
     */
    public boolean isEditable() {
        return this == DRAFT || this == PENDING_APPROVAL || this == FAILED;
    }
    
    /**
     * 检查状态是否可以执行
     * 
     * @return true if executable
     */
    public boolean isExecutable() {
        return this == APPROVED;
    }
}