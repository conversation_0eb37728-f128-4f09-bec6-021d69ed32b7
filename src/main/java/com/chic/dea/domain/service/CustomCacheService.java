package com.chic.dea.domain.service;

import com.chic.dea.apis.model.dto.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @classname CacheService
 * @description TODO
 * @date 2025/1/9 21:07
 */
@Service
@Slf4j
public class CustomCacheService {

    @CachePut(value = "jwt", key = "#jwt")
    public UserDTO putUser(String jwt, UserDTO userDTO) {
        log.info("put user");
        return userDTO;
    }

    @Cacheable(value = "jwt", key = "#jwt")
    public UserDTO getUser(String jwt) {
        log.info("get user");
        return null;
    }

}
