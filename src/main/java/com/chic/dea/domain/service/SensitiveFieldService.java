package com.chic.dea.domain.service;

import com.chic.dea.apis.model.dto.LineageTableFieldsVO;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 敏感字段检查服务接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface SensitiveFieldService {

    /**
     * 检查是否包含敏感字段
     * 
     * @param list 数据库表和字段列表
     * @return 敏感字段检查结果
     */
    List<SensitiveFieldCheckResult> checkSensitiveFields(List<LineageTableFieldsVO> list);

    /**
     * 敏感字段检查结果
     */
    class SensitiveFieldCheckResult {
        private final String tableName;
        private final String fieldName;
        private final String sensitiveFlag;

        public SensitiveFieldCheckResult(String tableName,String fieldName,String sensitiveFlag) {
            this.tableName = tableName;
            this.fieldName = fieldName;
            this.sensitiveFlag = sensitiveFlag;
        }

        public String getTableName() {
            return tableName;
        }

        public String getFieldName() {
            return fieldName;
        }

        public String getSensitiveFlag() {
            return sensitiveFlag;
        }
    }
}
