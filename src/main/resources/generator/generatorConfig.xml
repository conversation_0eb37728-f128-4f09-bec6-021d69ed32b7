<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!--数据库驱动-->
    <context id="baymax" targetRuntime="MyBatis3" defaultModelType="flat">
        <!--编码 -->
        <property name="javaFileEncoding" value="UTF-8"/>
        <!--<plugin type="com.founder.springboot.utils.mybatis.MySQLPaginationPlugin"/>-->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <plugin type="org.mybatis.generator.plugins.CaseInsensitiveLikePlugin"/>
        <!--额外配置的生成tostring()方法的插件，mybatis支持很多插件，这些插件都在 org.mybatis.generator.plugins包下 -->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin" />
        <!--注释配置 -->
        <commentGenerator>
            <!--是否产生注释，这是总的开关，true表示不生成注释，下面的三个配置都将失效 -->
            <property name="suppressAllComments" value="false" />

            <!--生成的注释中是否包含时间戳，默认false表示包含，为true则表示不包含 效果如:@mbg.generated Mon Oct 23
                11:42:12 CST 2017 -->
            <property name="suppressDate" value="true" />

            <!--当属性为false或未指定时，生成元素时，所有生成的注释将不包括表和列注释 -->
            <property name="addRemarkComments" value="true" />

            <!--格式化suppressDate中的时间戳 效果如:@mbg.generated 2017-10-23 11:58:43 -->
            <!-- <property name="dateFormat" value="yyyy-MM-dd HH:mm:ss" /> -->
        </commentGenerator>
        <!--数据库链接地址账号密码 -->
        <!--只用它生成 entity 不自动生成mapper.xml -->
        <jdbcConnection
                driverClass="oracle.jdbc.driver.OracleDriver"
                connectionURL="******************************************"
                userId="chicbusiness"
                password="chicbusiness">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="true"/>
        </javaTypeResolver>
        <!--生成Model类存放位置 -->
        <javaModelGenerator targetPackage="com.chic.dea.domain.database.avatar.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!--生成xml映射文件存放位置 -->
        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
        <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!--生成mapper类存放位置-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.chic.dea.domain.database.avatar.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
<!--        <table tableName="out_menu_info" domainObjectName="OutMenuInfo" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
<!--               selectByExampleQueryId="false" >-->
<!--            <generatedKey column="id" sqlStatement="Mysql"/>-->
<!--        </table>-->
<!--        <table tableName="out_user_info" domainObjectName="OutUserInfo" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
<!--               selectByExampleQueryId="false" >-->
<!--            <generatedKey column="id" sqlStatement="Mysql"/>-->
<!--        </table>-->
        <table tableName="PRPJPOLICYPLAN" domainObjectName="Prpjpolicyplan" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false" />
    </context>
</generatorConfiguration>
