package com.chic.dea.domain.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chic.dea.domain.database.entity.TaskExecutionQueue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务执行队列Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Mapper
public interface TaskExecutionQueueMapper extends BaseMapper<TaskExecutionQueue> {

    /**
     * 根据任务ID查询队列记录
     * 
     * @param taskId 任务ID
     * @return 队列记录
     */
    @Select("SELECT * FROM task_execution_queue WHERE task_id = #{taskId} LIMIT 1")
    TaskExecutionQueue findByTaskId(@Param("taskId") Long taskId);

    /**
     * 查询待执行的队列记录
     * 
     * @param currentTime 当前时间
     * @return 待执行队列列表
     */
    @Select("SELECT * FROM task_execution_queue " +
            "WHERE queue_status = 'PENDING' " +
            "AND next_execute_time <= #{currentTime} " +
            "ORDER BY priority DESC, next_execute_time ASC " +
            "LIMIT 10")
    List<TaskExecutionQueue> findPendingQueue(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询指定状态的队列记录
     * 
     * @param status 队列状态
     * @return 队列记录列表
     */
    @Select("SELECT * FROM task_execution_queue WHERE queue_status = #{status} ORDER BY create_at DESC")
    List<TaskExecutionQueue> findByStatus(@Param("status") String status);

    /**
     * 查询正在处理的队列记录
     * 
     * @return 处理中的队列列表
     */
    @Select("SELECT * FROM task_execution_queue WHERE queue_status = 'PROCESSING' ORDER BY create_at ASC")
    List<TaskExecutionQueue> findProcessingQueue();

    /**
     * 查询失败且可重试的队列记录
     * 
     * @param currentTime 当前时间
     * @return 可重试的队列列表
     */
    @Select("SELECT * FROM task_execution_queue " +
            "WHERE queue_status = 'FAILED' " +
            "AND retry_count < max_retry " +
            "AND next_execute_time <= #{currentTime} " +
            "ORDER BY next_execute_time ASC " +
            "LIMIT 5")
    List<TaskExecutionQueue> findRetryableQueue(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 更新队列状态
     * 
     * @param id 队列ID
     * @param status 新状态
     */
    @Update("UPDATE task_execution_queue SET queue_status = #{status} WHERE id = #{id}")
    void updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 更新重试信息
     * 
     * @param id 队列ID
     * @param retryCount 重试次数
     * @param nextExecuteTime 下次执行时间
     * @param status 状态
     */
    @Update("UPDATE task_execution_queue SET " +
            "retry_count = #{retryCount}, " +
            "next_execute_time = #{nextExecuteTime}, " +
            "queue_status = #{status} " +
            "WHERE id = #{id}")
    void updateRetryInfo(@Param("id") Long id,
                        @Param("retryCount") Integer retryCount,
                        @Param("nextExecuteTime") LocalDateTime nextExecuteTime,
                        @Param("status") String status);

    /**
     * 清理已完成的队列记录（保留最近30天）
     * 
     * @param cutoffTime 截止时间
     */
    @Update("DELETE FROM task_execution_queue " +
            "WHERE queue_status IN ('COMPLETED', 'FAILED') " +
            "AND create_at < #{cutoffTime}")
    void cleanupOldRecords(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计各状态的队列数量
     * 
     * @return 状态统计结果
     */
    @Select("SELECT queue_status, COUNT(1) as count " +
            "FROM task_execution_queue " +
            "GROUP BY queue_status")
    List<java.util.Map<String, Object>> countByStatus();

    /**
     * 查询超时的处理中队列（超过2小时未更新状态）
     * 
     * @param timeoutTime 超时时间
     * @return 超时的队列列表
     */
    @Select("SELECT * FROM task_execution_queue " +
            "WHERE queue_status = 'PROCESSING' " +
            "AND update_at < #{timeoutTime}")
    List<TaskExecutionQueue> findTimeoutProcessingQueue(@Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 重置超时队列状态为待执行
     * 
     * @param timeoutTime 超时时间
     */
    @Update("UPDATE task_execution_queue SET " +
            "queue_status = 'PENDING', " +
            "next_execute_time = NOW() " +
            "WHERE queue_status = 'PROCESSING' " +
            "AND update_at < #{timeoutTime}")
    void resetTimeoutQueue(@Param("timeoutTime") LocalDateTime timeoutTime);
}