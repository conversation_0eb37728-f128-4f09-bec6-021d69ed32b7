package com.chic.dea.domain.service;

import com.chic.dea.apis.model.dto.LineageTableFieldsVO;
import com.chic.dea.domain.database.entity.ColumnClassify;
import com.chic.dea.domain.database.mapper.SensitiveFieldMapper;
import com.chic.dea.domain.service.impl.SensitiveFieldServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 敏感字段服务测试类
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@ExtendWith(MockitoExtension.class)
class SensitiveFieldServiceTest {

    @Mock
    private SensitiveFieldMapper sensitiveFieldMapper;

    @InjectMocks
    private SensitiveFieldServiceImpl sensitiveFieldService;

    private LineageTableFieldsVO testTableFields;
    private ColumnClassify testSensitiveField;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testTableFields = LineageTableFieldsVO.builder()
            .tableName("USER_INFO")
            .fieldList(Arrays.asList("user_id", "user_name", "phone", "email"))
            .build();

        testSensitiveField = new ColumnClassify();
        testSensitiveField.setTableName("USER_INFO");
        testSensitiveField.setColumnName("phone");
        testSensitiveField.setSensitiveFlag("高敏感");
        testSensitiveField.setIsSensitiveColumn("1");
    }

    @Test
    void testCheckSensitiveFields_WithSensitiveFields() {
        // 模拟mapper返回敏感字段
        when(sensitiveFieldMapper.findSensitiveFields(anyString(), anyList()))
            .thenReturn(Arrays.asList(testSensitiveField));

        // 执行测试
        List<SensitiveFieldService.SensitiveFieldCheckResult> results = 
            sensitiveFieldService.checkSensitiveFields(Arrays.asList(testTableFields));

        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        
        SensitiveFieldService.SensitiveFieldCheckResult result = results.get(0);
        assertEquals("USER_INFO", result.getTableName());
        assertEquals("phone", result.getFieldName());
        assertEquals("高敏感", result.getSensitiveFlag());
    }

    @Test
    void testCheckSensitiveFields_NoSensitiveFields() {
        // 模拟mapper返回空列表
        when(sensitiveFieldMapper.findSensitiveFields(anyString(), anyList()))
            .thenReturn(Collections.emptyList());

        // 执行测试
        List<SensitiveFieldService.SensitiveFieldCheckResult> results = 
            sensitiveFieldService.checkSensitiveFields(Arrays.asList(testTableFields));

        // 验证结果
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testCheckSensitiveFields_EmptyInput() {
        // 执行测试
        List<SensitiveFieldService.SensitiveFieldCheckResult> results = 
            sensitiveFieldService.checkSensitiveFields(Collections.emptyList());

        // 验证结果
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testCheckSensitiveFields_NullInput() {
        // 执行测试
        List<SensitiveFieldService.SensitiveFieldCheckResult> results = 
            sensitiveFieldService.checkSensitiveFields(null);

        // 验证结果
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testCheckSensitiveFields_MultipleTablesWithSensitiveFields() {
        // 准备多个表的测试数据
        LineageTableFieldsVO table1 = LineageTableFieldsVO.builder()
            .tableName("USER_INFO")
            .fieldList(Arrays.asList("user_id", "phone"))
            .build();

        LineageTableFieldsVO table2 = LineageTableFieldsVO.builder()
            .tableName("ORDER_INFO")
            .fieldList(Arrays.asList("order_id", "customer_phone"))
            .build();

        ColumnClassify sensitiveField1 = new ColumnClassify();
        sensitiveField1.setTableName("USER_INFO");
        sensitiveField1.setColumnName("phone");
        sensitiveField1.setSensitiveFlag("高敏感");

        ColumnClassify sensitiveField2 = new ColumnClassify();
        sensitiveField2.setTableName("ORDER_INFO");
        sensitiveField2.setColumnName("customer_phone");
        sensitiveField2.setSensitiveFlag("中敏感");

        // 模拟mapper对不同表的返回
        when(sensitiveFieldMapper.findSensitiveFields("USER_INFO", Arrays.asList("user_id", "phone")))
            .thenReturn(Arrays.asList(sensitiveField1));
        when(sensitiveFieldMapper.findSensitiveFields("ORDER_INFO", Arrays.asList("order_id", "customer_phone")))
            .thenReturn(Arrays.asList(sensitiveField2));

        // 执行测试
        List<SensitiveFieldService.SensitiveFieldCheckResult> results = 
            sensitiveFieldService.checkSensitiveFields(Arrays.asList(table1, table2));

        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        
        // 验证第一个敏感字段
        SensitiveFieldService.SensitiveFieldCheckResult result1 = results.get(0);
        assertEquals("USER_INFO", result1.getTableName());
        assertEquals("phone", result1.getFieldName());
        assertEquals("高敏感", result1.getSensitiveFlag());
        
        // 验证第二个敏感字段
        SensitiveFieldService.SensitiveFieldCheckResult result2 = results.get(1);
        assertEquals("ORDER_INFO", result2.getTableName());
        assertEquals("customer_phone", result2.getFieldName());
        assertEquals("中敏感", result2.getSensitiveFlag());
    }
}
