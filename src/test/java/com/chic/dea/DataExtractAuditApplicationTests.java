package com.chic.dea;

import org.jasypt.util.text.BasicTextEncryptor;

import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class DataExtractAuditApplicationTests {

  @Autowired
  private RedissonClient redissonClient;
  @Test
  void contextLoads() {
    RLock lock = redissonClient.getLock("lockKey");
    try {
      lock.lock();
      Thread.sleep(10000);
    } catch (Exception e) {

    } finally {
      if (lock.isLocked() && lock.isHeldByCurrentThread()) {
        lock.unlock();
      }
    }
    System.out.println("已解锁");

  }

  public static void main(String[] args) {
    BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
    //加密所需的salt(盐)
    textEncryptor.setPassword("EbfYkitulv73I2p0mXI50JMXoaxZTKJ7");
    //要加密的数据（数据库的用户名或密码）
    String username = textEncryptor.encrypt("chicbusiness");
    String password = textEncryptor.encrypt("chicbusiness");
    System.out.println("username:"+username);
    System.out.println("password:"+password);
  }

}
