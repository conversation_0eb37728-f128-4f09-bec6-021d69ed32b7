package com.chic.dea.domain.database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
/**
 * 元数据字段分类实体类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@TableName("DWS_METADATA_COLUMN_CLASSIFY")
public class ColumnClassify {

    /**
     * 实例ID
     */
    @TableId("INSTANCE_ID")
    private String instanceId;

    /**
     * 表名
     */
    @TableField("TABLE_NAME")
    private String tableName;

    /**
     * 字段名
     */
    @TableField("COLUMN_NAME")
    private String columnName;

    /**
     * 敏感标识
     */
    @TableField("SENSITIVE_FLAG")
    private String sensitiveFlag;

    /**
     * 是否为敏感字段
     */
    @TableField("IS_SENSITIVE_COLUMN")
    private String isSensitiveColumn;

}
