package com.chic.dea.domain.database.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chic.dea.domain.database.entity.OAInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * OA信息Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-11
 */
@Mapper
@DS("oa") // 指定使用OA数据源
public interface OAInfoMapper extends BaseMapper<OAInfo> {

    /**
     * 根据OA ID查询OA信息
     * 
     * @param tableName 表名
     * @param lcid OA ID
     * @return OA信息
     */
    OAInfo findByLcid(@Param("tableName") String tableName, @Param("lcid") String lcid);
}