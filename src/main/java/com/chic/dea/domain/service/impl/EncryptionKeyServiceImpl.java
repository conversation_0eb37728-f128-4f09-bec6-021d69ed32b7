package com.chic.dea.domain.service.impl;

import com.chic.dea.domain.service.DictionaryService;
import com.chic.dea.domain.service.EncryptionKeyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 加密密钥服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EncryptionKeyServiceImpl implements EncryptionKeyService {

    private final DictionaryService dictionaryService;

    // 字典编码
    private static final String SYSTEM_CONFIG_DICT_CODE = "SYSTEM_CONFIG";
    // 数据库密码加密密钥的字典项编码
    private static final String DATABASE_PASSWORD_KEY_ITEM_CODE = "DATABASE_PASSWORD_KEY";

    @Override
    public String getDatabasePasswordKey() {
        try {
            log.debug("从数据库获取密码加密密钥");
            
            String key = dictionaryService.getDictionaryItemValue(SYSTEM_CONFIG_DICT_CODE, DATABASE_PASSWORD_KEY_ITEM_CODE);
            
            if (StringUtils.hasText(key)) {
                log.debug("成功从数据库获取到加密密钥");
                return key;
            }else{
                log.error("从数据库获取加密密钥失败，数据库中未配置加密密钥");
                return null;
            }

        } catch (Exception e) {
            log.error("从数据库获取加密密钥失败", e);
            return null;
        }
    }
}