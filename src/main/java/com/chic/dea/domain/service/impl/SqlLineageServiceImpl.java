package com.chic.dea.domain.service.impl;

import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.dea.apis.model.base.ApiResponse;
import com.chic.dea.apis.model.dto.LineageTableFieldsVO;
import com.chic.dea.domain.service.SqlLineageService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;

@Slf4j
@Service
public class SqlLineageServiceImpl implements SqlLineageService {

    @Value("${custom.sql-lineage.url}")
    private String lineageUrl;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<LineageTableFieldsVO> extractTableFields(String sql, String dialect) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "sql不能为空"));
        }

        String requestJson = buildRequestJson(sql, dialect);
        String respBody = null;
        try {
            respBody = doPost(lineageUrl, requestJson);
            if (respBody == null || respBody.isEmpty()) {
                throw new ApiException(new ErrorResult(ErrorResultCode.QUERY_RESULT_ISNULL.getErrorCode(), "血缘服务返回为空"));
            }
            return parseAndAggregate(respBody);
        } catch (Exception ex) {
            log.error("调用血缘服务失败: {}", ex.getMessage(), ex);
            throw new ApiException(new ErrorResult(ErrorResultCode.QUERY_RESULT_ISNULL.getErrorCode(), "调用血缘服务失败"));
        }
    }

    private String buildRequestJson(String sql, String dialect) {
        String d = (dialect == null || dialect.trim().isEmpty()) ? "mysql" : dialect.trim();
        Map<String, Object> req = new LinkedHashMap<>();
        req.put("sql", sql);
        req.put("dialect", d);
        req.put("verbose", false);
        req.put("silent_mode", false);
        req.put("file_path", ".");
        try {
            return objectMapper.writeValueAsString(req);
        } catch (Exception e) {
            // 兜底简单拼接
            return "{\"sql\":" + quote(sql) + ",\"dialect\":" + quote(d) + ",\"verbose\":false,\"silent_mode\":false,\"file_path\":\".\"}";
        }
    }

    private String quote(String s) {
        try {
            return objectMapper.writeValueAsString(s);
        } catch (Exception e) {
            return "\"" + String.valueOf(s).replace("\"", "\\\"") + "\"";
        }
    }

    private String doPost(String url, String json) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(json, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new IllegalStateException("Unexpected code " + response.getStatusCodeValue());
        }
        return response.getBody();
    }

    private List<LineageTableFieldsVO> parseAndAggregate(String json) throws IOException {
        JsonNode root = objectMapper.readTree(json);
        JsonNode columnLineage = root.get("column_lineage");
        if (columnLineage == null || !columnLineage.isArray()) {
            return Collections.emptyList();
        }

        Map<String, Set<String>> tableToFields = new LinkedHashMap<>();

        for (JsonNode pathNode : columnLineage) {
            if (pathNode == null || !pathNode.isArray() || pathNode.size() == 0) {
                continue;
            }
            String first = pathNode.get(0).asText("");
            if (first.isEmpty()) {
                continue;
            }

            // 期望形如: "<default>.sales.region"
            String[] parts = first.split("\\.");
            if (parts.length >= 3) {
                String table = parts[1];
                String field = parts[2];
                if (!table.isEmpty() && !field.isEmpty()) {
                    tableToFields.computeIfAbsent(table, k -> new LinkedHashSet<>()).add(field);
                }
            }
        }

        List<LineageTableFieldsVO> result = new ArrayList<>();
        for (Map.Entry<String, Set<String>> e : tableToFields.entrySet()) {
            result.add(LineageTableFieldsVO.builder()
                .tableName(e.getKey())
                .fieldList(new ArrayList<>(e.getValue()))
                .build());
        }
        return result;
    }
}


