package com.chic.dea.apis.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典项视图对象VO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class DictionaryItemVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 所属字典ID
     */
    private Long dictionaryId;

    /**
     * 字典编码
     */
    private String dictionaryCode;

    /**
     * 字典名称
     */
    private String dictionaryName;

    /**
     * 字典项编码
     */
    private String code;

    /**
     * 字典项名称
     */
    private String name;

    /**
     * 字典项值
     */
    private String value;

    /**
     * 字典项描述
     */
    private String description;

    /**
     * 排序索引
     */
    private Integer orderIndex;

    /**
     * 状态（1: 启用, 0: 停用）
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
}