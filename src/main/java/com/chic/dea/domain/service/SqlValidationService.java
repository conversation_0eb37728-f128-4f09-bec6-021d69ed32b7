package com.chic.dea.domain.service;


/**
 * SQL校验服务接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface SqlValidationService {

    /**
     * 验证SQL语法
     *
     * @param sql SQL语句
     * @param databaseType 数据库类型（MYSQL, ORACLE等）
     * @return 校验结果
     */
    SqlValidationResult validateSyntax(String sql, String databaseType);

    /**
     * 为SQL添加LIMIT子句
     * 
     * @param sql 原始SQL
     * @param limit 限制条数
     * @param databaseType 数据库类型（MYSQL, ORACLE等）
     * @return 添加LIMIT后的SQL
     */
    String addLimitClause(String sql, int limit, String databaseType);

    /**
     * 在原始SQL前拼接生成视图语句
     * 格式：CREATE VIEW temp_view AS {sql}
     *
     * @param sql 原始SQL
     * @return 包含CREATE VIEW子句的SQL
     */
    String createViewClause(String sql);


    /**
     * SQL校验结果
     */
    class SqlValidationResult {
        private boolean valid;
        private String errorMessage;
        private String errorCode;

        public SqlValidationResult(boolean valid, String errorMessage, String errorCode) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.errorCode = errorCode;
        }

        public static SqlValidationResult success() {
            return new SqlValidationResult(true, null, null);
        }

        public static SqlValidationResult fail(String errorMessage) {
            return new SqlValidationResult(false, errorMessage, null);
        }

        public static SqlValidationResult fail(String errorMessage, String errorCode) {
            return new SqlValidationResult(false, errorMessage, errorCode);
        }

        public boolean isValid() { return valid; }
        public String getErrorMessage() { return errorMessage; }
        public String getErrorCode() { return errorCode; }
    }

}
