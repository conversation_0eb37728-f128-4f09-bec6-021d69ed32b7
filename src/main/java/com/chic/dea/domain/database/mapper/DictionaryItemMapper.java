package com.chic.dea.domain.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chic.dea.domain.database.entity.DictionaryItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 字典项Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Mapper
public interface DictionaryItemMapper extends BaseMapper<DictionaryItem> {

    /**
     * 根据字典ID查询启用状态的字典项列表
     * 
     * @param dictionaryId 字典ID
     * @return 字典项列表
     */
    @Select("SELECT * FROM dea_dictionary_item WHERE dictionary_id = #{dictionaryId} AND status = 1 ORDER BY order_index ASC, id ASC")
    List<DictionaryItem> findByDictionaryId(@Param("dictionaryId") Long dictionaryId);

    /**
     * 根据字典编码查询启用状态的字典项列表
     * 
     * @param dictionaryCode 字典编码
     * @return 字典项列表
     */
    @Select("SELECT di.* FROM dea_dictionary_item di " +
            "INNER JOIN dea_dictionary d ON di.dictionary_id = d.id " +
            "WHERE d.code = #{dictionaryCode} AND di.status = 1 AND d.status = 1 " +
            "ORDER BY di.order_index ASC, di.id ASC")
    List<DictionaryItem> findByDictionaryCode(@Param("dictionaryCode") String dictionaryCode);

    /**
     * 根据字典编码和字典项编码查询字典项值
     * 
     * @param dictionaryCode 字典编码
     * @param itemCode 字典项编码
     * @return 字典项值
     */
    @Select("SELECT di.value FROM dea_dictionary_item di " +
            "INNER JOIN dea_dictionary d ON di.dictionary_id = d.id " +
            "WHERE d.code = #{dictionaryCode} AND di.code = #{itemCode} AND di.status = 1 AND d.status = 1")
    String findValueByDictionaryCodeAndItemCode(@Param("dictionaryCode") String dictionaryCode, @Param("itemCode") String itemCode);
}