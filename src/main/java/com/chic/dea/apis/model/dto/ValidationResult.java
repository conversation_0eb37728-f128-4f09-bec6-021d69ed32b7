package com.chic.dea.apis.model.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * SQL校验结果DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@Builder
public class ValidationResult {

    /**
     * 是否校验通过
     */
    private boolean valid;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 涉及的敏感字段
     */
    private Set<String> sensitiveFields;

    /**
     * 涉及的表名
     */
    private Set<String> tables;

    /**
     * 涉及的字段
     */
    private Set<String> fields;

    /**
     * 是否需要合规审批
     */
    private boolean requiresCompliance;

    /**
     * 表和字段的实体集合
     */
    private List<TableFieldEntity> tableFieldEntities;

    /**
     * 创建成功结果
     */
    public static ValidationResult success() {
        return ValidationResult.builder()
                .valid(true)
                .requiresCompliance(false)
                .build();
    }

    /**
     * 创建失败结果
     */
    public static ValidationResult fail(String errorMessage) {
        return ValidationResult.builder()
                .valid(false)
                .errorMessage(errorMessage)
                .build();
    }

    /**
     * 创建需要合规审批的结果
     */
    public static ValidationResult requiresCompliance(Set<String> sensitiveFields) {
        return ValidationResult.builder()
                .valid(true)
                .requiresCompliance(true)
                .sensitiveFields(sensitiveFields)
                .build();
    }
}