package com.chic.dea.apis.controller;

import com.chic.commons.base.Result;
import com.chic.dea.apis.model.dto.UserDTO;
import com.chic.dea.domain.service.CustomAuthService;
import com.chic.dea.infrastructure.general.CurrentUser;
import com.chic.skyauth.dto.LoginDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 权限管理接口
 * <AUTHOR>
 * @classname LoginController
 * @date 2025/1/8 21:33
 */
@RestController
@RequestMapping("/df/api/auth")
public class AuthController {
    @Autowired
    private CustomAuthService customAuthService;


    /**
     * 登录接口
     * @param loginDto
     * @return
     */
    @PostMapping("/login")
    public Result<Map> login(@RequestBody LoginDto loginDto) {
        return Result.success(customAuthService.login(loginDto));
    }
    /**
     * 查询用户
     * @param userDTO
     * @return
     */
    @PostMapping("/select/user")
    public Result<UserDTO> selectUser(@CurrentUser UserDTO userDTO) {
        return Result.success(userDTO);
    }
    /**
     * 刷新token
     * @param token
     * @return
     */
    @PostMapping("/refresh/token")
    public Result<Map> refreshToken(@RequestParam String token) {
        return Result.success(customAuthService.refreshToken(token));
    }
    /**
     * 查询用户列表
     * @param userDTO
     * @return
     */
    @PostMapping("/select/users")
    public Result<List<UserDTO>> selectUsers(@CurrentUser UserDTO userDTO) {
        return Result.success(customAuthService.selectUsersFromServer(userDTO.getToken()));
    }

}
