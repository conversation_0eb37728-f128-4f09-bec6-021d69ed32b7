
server:
  servlet:
    context-path: /data-extract-audit
  port: 8248
  shutdown: graceful

spring:
  application:
    name: '@project.name@'
    version: '@project.version@'
    branch: '@scmBranch@'
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  profiles:
    active: dev
#  boot:
#    admin:
#      client:
#        url: http://${ci.environment.slug}-monitor.chinahuanong.com.cn/monitor
#        instance:
#          name: ${spring.application.name}
#          prefer-ip: true
