package com.chic.dea;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@MapperScan(basePackages = {"com.chic.dea.**.mapper"})
@ComponentScan({"com.chic.commons.**","com.chic.**"})
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class})
public class DataExtractAuditApplication {

    public static void main(String[] args) {
        SpringApplication.run(DataExtractAuditApplication.class, args);
    }

}
