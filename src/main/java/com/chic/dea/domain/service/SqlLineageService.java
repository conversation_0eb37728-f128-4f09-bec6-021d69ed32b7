package com.chic.dea.domain.service;

import com.chic.dea.apis.model.dto.LineageTableFieldsVO;

import java.util.List;

/**
 * SQL 血缘服务：调用外部血缘解析服务并提取物理表及字段
 */
public interface SqlLineageService {

    /**
     * 从 SQL 中提取物理表及字段（按表聚合字段名）
     * 仅使用外部血缘接口返回的 column_lineage 中每条链路的第一个元素 <db>.<table>.<column>
     *
     * @param sql 原始 SQL
     * @param dialect 方言，如 mysql、oracle
     * @return 按表聚合后的结果列表
     */
    List<LineageTableFieldsVO> extractTableFields(String sql, String dialect);
}


