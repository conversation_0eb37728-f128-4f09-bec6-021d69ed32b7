package com.chic.dea.infrastructure.general;

import com.chic.commons.util.StringUtils;
import com.chic.dea.apis.model.dto.UserDTO;
import com.chic.dea.domain.service.CustomAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @classname JwtInterceptor
 * @description TODO
 * @date 2025/1/9 20:28
 */
@Component
public class JwtInterceptor implements HandlerInterceptor {

    @Autowired
    private CustomAuthService customAuthService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);

            UserDTO userDTO = customAuthService.selectUser(token);
            if (userDTO != null && StringUtils.isNotBlank(userDTO.getUserName())){
                return true;
            }
        }
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        return false;
    }


}
