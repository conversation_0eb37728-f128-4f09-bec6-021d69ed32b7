package com.chic.dea.domain.service.impl;

import com.chic.dea.domain.service.SqlValidationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.sql.*;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.parser.SqlParserPos;
import org.apache.calcite.sql.validate.SqlConformanceEnum;
import org.apache.calcite.avatica.util.Casing;
import org.apache.calcite.avatica.util.Quoting;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;
import java.util.Map;
import java.util.HashMap;

/**
 * SQL校验服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
public class SqlValidationServiceImpl implements SqlValidationService {

    // 支持的数据库类型
    public enum DatabaseType {
        MYSQL, ORACLE, UNKNOWN
    }

    // 不同数据库的解析器配置
    private final Map<DatabaseType, SqlParser.Config> parserConfigs;

    public SqlValidationServiceImpl() {
        this.parserConfigs = new HashMap<>();
        initializeParserConfigs();
    }

    /**
     * 初始化不同数据库的解析器配置
     */
    private void initializeParserConfigs() {
        // MySQL配置 - 支持反引号和双引号
        SqlParser.Config mysqlConfig = SqlParser.config()
            .withIdentifierMaxLength(256)
            .withQuoting(Quoting.DOUBLE_QUOTE)  // 支持双引号
            .withQuotedCasing(Casing.UNCHANGED)  // 保持引用标识符的大小写
            .withUnquotedCasing(Casing.UNCHANGED)  // 保持非引用标识符的大小写
            .withConformance(SqlConformanceEnum.MYSQL_5);  // 使用MySQL 5兼容性

        // Oracle配置 - 支持双引号
        SqlParser.Config oracleConfig = SqlParser.config()
            .withIdentifierMaxLength(256)
            .withQuoting(Quoting.DOUBLE_QUOTE)  // Oracle使用双引号
            .withQuotedCasing(Casing.UNCHANGED)  // 保持引用标识符的大小写
            .withUnquotedCasing(Casing.TO_UPPER)  // Oracle非引用标识符默认大写
            .withConformance(SqlConformanceEnum.ORACLE_12);  // 使用Oracle 12兼容性

        parserConfigs.put(DatabaseType.MYSQL, mysqlConfig);
        parserConfigs.put(DatabaseType.ORACLE, oracleConfig);
    }

    /**
     * 根据字符串获取数据库类型枚举
     */
    private DatabaseType parseDatabaseType(String databaseType) {
        if (databaseType == null || databaseType.trim().isEmpty()) {
            return DatabaseType.MYSQL; // 默认使用MySQL
        }
        
        String upperType = databaseType.trim().toUpperCase();
        switch (upperType) {
            case "MYSQL":
                return DatabaseType.MYSQL;
            case "ORACLE":
                return DatabaseType.ORACLE;
            default:
                log.warn("不支持的数据库类型: {}, 使用默认MySQL配置", databaseType);
                return DatabaseType.MYSQL;
        }
    }

    /**
     * 获取指定数据库类型的解析器配置
     */
    private SqlParser.Config getParserConfig(DatabaseType dbType) {
        return parserConfigs.getOrDefault(dbType, parserConfigs.get(DatabaseType.MYSQL));
    }

    /**
     * 使用指定数据库类型验证SQL语法
     */
    private SqlValidationResult validateSyntaxWithDatabaseType(String sql, DatabaseType dbType) {
        try {
            SqlParser.Config config = getParserConfig(dbType);
            SqlParser parser = SqlParser.create(sql, config);
            SqlNode sqlNode = parser.parseQuery();
            
            // 检查是否为SELECT或WITH语句
            if (!(sqlNode instanceof SqlSelect) && !(sqlNode instanceof SqlWith)) {
                return SqlValidationResult.fail("只允许执行SELECT或WITH查询语句", "NOT_SELECT_OR_WITH");
            }
            
            log.debug("SQL语法校验通过，使用{}配置", dbType);
            return SqlValidationResult.success();
            
        } catch (SqlParseException e) {
            log.debug("使用{}配置解析失败: {}", dbType, e.getMessage());
            return SqlValidationResult.fail("SQL语法错误: " + e.getMessage(), "SYNTAX_ERROR");
        } catch (Exception e) {
            log.debug("使用{}配置校验异常: {}", dbType, e.getMessage());
            return SqlValidationResult.fail("SQL校验失败: " + e.getMessage(), "VALIDATION_ERROR");
        }
    }

    @Override
    public SqlValidationResult validateSyntax(String sql, String databaseType) {
        log.debug("开始校验SQL语法, 数据库类型: {}", databaseType);
        
        if (sql == null || sql.trim().isEmpty()) {
            return SqlValidationResult.fail("SQL语句不能为空");
        }

        // 移除SQL注释和多余空格，支持双引号别名
        String cleanSql = cleanSQL(sql);
        
        // 基本安全检查
        SqlValidationResult securityCheck = performSecurityCheck(cleanSql);
        if (!securityCheck.isValid()) {
            return securityCheck;
        }

        // 解析数据库类型并验证
        DatabaseType dbType = parseDatabaseType(databaseType);
        return validateSyntaxWithDatabaseType(cleanSql, dbType);
    }

    

    @Override
    public String addLimitClause(String sql, int limit, String databaseType) {
        log.debug("为SQL添加LIMIT子句, limit: {}, 数据库类型: {}", limit, databaseType);
        
        String cleanSql = cleanSQL(sql);
        DatabaseType dbType = parseDatabaseType(databaseType);
        
        return addLimitClauseWithDatabaseType(cleanSql, limit, dbType);
    }

    @Override
    public String createViewClause(String sql) {
        if (sql == null) {
            return "CREATE VIEW temp_view AS";
        }
        String trimmed = sql.trim();
        while (trimmed.endsWith(";")) {
            trimmed = trimmed.substring(0, trimmed.length() - 1).trim();
        }

        Pattern prefix = Pattern.compile("^\\s*CREATE\\s+VIEW\\s+temp_view\\s+AS\\b", Pattern.CASE_INSENSITIVE);
        if (prefix.matcher(trimmed).find()) {
            return trimmed;
        }

        return "CREATE VIEW temp_view AS\n" + trimmed;
    }


    /**
     * 使用指定数据库类型添加LIMIT子句
     */
    private String addLimitClauseWithDatabaseType(String sql, int limit, DatabaseType dbType) {
        try {
            SqlParser.Config config = getParserConfig(dbType);
            SqlParser parser = SqlParser.create(sql, config);
            SqlNode sqlNode = parser.parseQuery();
            
            if (sqlNode instanceof SqlSelect) {
                SqlSelect select = (SqlSelect) sqlNode;
                
                // 如果已经有LIMIT子句，返回原SQL
                if (select.getFetch() != null) {
                    log.debug("SQL已包含LIMIT子句");
                    return sql;
                }
                
                // 创建新的LIMIT子句
                SqlNumericLiteral limitNode = SqlLiteral.createExactNumeric(
                    String.valueOf(limit), SqlParserPos.ZERO);
                    
                // 克隆SELECT节点并添加LIMIT
                SqlSelect newSelect = (SqlSelect) select.clone(SqlParserPos.ZERO);
                newSelect.setFetch(limitNode);
                
                String resultSql = newSelect.toString();
                log.debug("添加LIMIT子句完成: {}", resultSql);
                return resultSql;
            } else if (sqlNode instanceof SqlWith) {
                // 对于WITH语句，检查最外层的SELECT
                SqlWith withStatement = (SqlWith) sqlNode;
                if (withStatement.body instanceof SqlSelect) {
                    SqlSelect bodySelect = (SqlSelect) withStatement.body;
                    
                    // 如果已经有LIMIT子句，返回原SQL
                    if (bodySelect.getFetch() != null) {
                        log.debug("WITH语句的主体SELECT已包含LIMIT子句");
                        return sql;
                    }
                    
                    // 创建新的LIMIT子句
                    SqlNumericLiteral limitNode = SqlLiteral.createExactNumeric(
                        String.valueOf(limit), SqlParserPos.ZERO);
                        
                    // 克隆SELECT节点并添加LIMIT
                    SqlSelect newBodySelect = (SqlSelect) bodySelect.clone(SqlParserPos.ZERO);
                    newBodySelect.setFetch(limitNode);
                    
                    // 克隆WITH节点并更新主体
                    SqlWith newWith = (SqlWith) withStatement.clone(SqlParserPos.ZERO);
                    newWith.body = newBodySelect;
                    
                    String resultSql = newWith.toString();
                    log.debug("为WITH语句添加LIMIT子句完成: {}", resultSql);
                    return resultSql;
                }
            }
            
            return sql;
            
        } catch (SqlParseException e) {
            log.warn("解析SQL失败，使用简单字符串拼接方式添加LIMIT: {}", e.getMessage());
            return addLimitByStringOperation(sql, limit);
        } catch (Exception e) {
            log.warn("添加LIMIT子句异常，使用简单字符串拼接方式: {}", e.getMessage());
            return addLimitByStringOperation(sql, limit);
        }
    }

    /**
     * 清理SQL语句（移除注释、多余空格等）
     */
    private String cleanSQL(String sql) {
        if (sql == null) {
            return "";
        }
        
        // 移除单行注释
        sql = sql.replaceAll("--.*?\\n", "\n");
        
        // 移除多行注释
        sql = sql.replaceAll("/\\*.*?\\*/", "");
        
        // 预处理别名 - 支持双引号别名
        sql = preprocessAliases(sql);
        
        // 移除多余的空白字符
        sql = sql.replaceAll("\\s+", " ").trim();
        
        return sql;
    }

    /**
     * 预处理别名，支持双引号、单引号和反引号
     */
    private String preprocessAliases(String sql) {
        // 将单引号别名转换为双引号别名（更通用）
        // 匹配 AS '别名' 模式，将单引号替换为双引号
        Pattern singleQuoteAliasPattern = Pattern.compile("\\bAS\\s+'([^']+)'", Pattern.CASE_INSENSITIVE);
        sql = singleQuoteAliasPattern.matcher(sql).replaceAll("AS \"$1\"");
        
        // 处理不带AS的单引号别名
        // 匹配表名或字段名后直接跟单引号的情况
        Pattern directSingleQuotePattern = Pattern.compile("(\\w+)\\s+'([^']+)'", Pattern.CASE_INSENSITIVE);
        sql = directSingleQuotePattern.matcher(sql).replaceAll("$1 \"$2\"");
        
        return sql;
    }

    /**
     * 安全检查（防止SQL注入等危险操作）
     */
    private SqlValidationResult performSecurityCheck(String sql) {
        String upperSql = sql.toUpperCase();
        
        // 检查危险关键字
        String[] dangerousKeywords = {
            "DROP", "DELETE", "UPDATE", "INSERT", "CREATE", "ALTER", 
            "TRUNCATE", "EXEC", "EXECUTE", "CALL", "LOAD_FILE", 
            "INTO OUTFILE", "INTO DUMPFILE"
        };
        
        for (String keyword : dangerousKeywords) {
            if (upperSql.contains(keyword)) {
                return SqlValidationResult.fail("SQL语句包含危险操作: " + keyword, "DANGEROUS_OPERATION");
            }
        }
        
        // 检查是否使用SELECT *（禁止查询所有列）
        if (containsSelectStar(sql)) {
            return SqlValidationResult.fail("不允许使用SELECT *查询所有列，请明确指定需要查询的字段", "SELECT_STAR_NOT_ALLOWED");
        }
        
        // 检查多语句（防止SQL注入）
        if (sql.contains(";") && !sql.trim().endsWith(";")) {
            return SqlValidationResult.fail("不允许执行多条SQL语句", "MULTIPLE_STATEMENTS");
        }
        
        return SqlValidationResult.success();
    }

    /**
     * 检查SQL语句是否包含SELECT *
     */
    private boolean containsSelectStar(String sql) {
        String upperSql = sql.toUpperCase();
        
        // 使用正则表达式匹配SELECT *模式
        // 匹配SELECT后跟空白字符，然后是*，然后是空白字符或FROM等关键字
        Pattern selectStarPattern = Pattern.compile("\\bSELECT\\s+\\*\\s*(?:FROM|$|,)", Pattern.CASE_INSENSITIVE);
        
        // 检查主查询中的SELECT *
        if (selectStarPattern.matcher(upperSql).find()) {
            return true;
        }
        
        // 检查子查询中的SELECT *
        // 寻找括号内的SELECT *
        Pattern subQueryPattern = Pattern.compile("\\(\\s*SELECT\\s+\\*\\s+", Pattern.CASE_INSENSITIVE);
        if (subQueryPattern.matcher(upperSql).find()) {
            return true;
        }
        
        // 检查WITH子句中的SELECT *
        Pattern withSelectStarPattern = Pattern.compile("\\bWITH\\s+\\w+\\s+AS\\s*\\(\\s*SELECT\\s+\\*\\s+", Pattern.CASE_INSENSITIVE);
        if (withSelectStarPattern.matcher(upperSql).find()) {
            return true;
        }
        
        return false;
    }

    /**
     * 通过字符串操作添加LIMIT（备用方案）
     */
    private String addLimitByStringOperation(String sql, int limit) {
        // 移除末尾的分号
        sql = sql.trim();
        if (sql.endsWith(";")) {
            sql = sql.substring(0, sql.length() - 1).trim();
        }
        
        // 检查是否已有LIMIT子句
        Pattern limitPattern = Pattern.compile("\\bLIMIT\\s+\\d+", Pattern.CASE_INSENSITIVE);
        if (limitPattern.matcher(sql).find()) {
            return sql;
        }
        
        return sql + " LIMIT " + limit;
    }

    /**
     * 统计字符串中指定子串的出现次数
     */
    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }

    
}
