package com.chic.dea.apis.model.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 预览数据响应DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@Builder
public class PreviewDataResponse {

    /**
     * 列名列表
     */
    private List<String> columns;

    /**
     * 数据行列表
     */
    private List<Map<String, Object>> rows;

    /**
     * 预览数据行数
     */
    private Integer rowCount;

    /**
     * 预计总记录数
     */
    private Long estimatedTotalRecords;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;

    /**
     * 创建成功响应
     */
    public static PreviewDataResponse success(List<String> columns, List<Map<String, Object>> rows, Long executionTime) {
        return PreviewDataResponse.builder()
                .success(true)
                .columns(columns)
                .rows(rows)
                .rowCount(rows.size())
                .executionTime(executionTime)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static PreviewDataResponse fail(String errorMessage) {
        return PreviewDataResponse.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
}