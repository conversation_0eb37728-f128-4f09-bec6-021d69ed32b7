package com.chic.dea.domain.database.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务执行队列实体类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@TableName("task_execution_queue")
public class TaskExecutionQueue {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 队列状态
     */
    @TableField("queue_status")
    private String queueStatus;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @TableField("max_retry")
    private Integer maxRetry;

    /**
     * 下次执行时间
     */
    @TableField("next_execute_time")
    private LocalDateTime nextExecuteTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    private LocalDateTime createAt;

    /**
     * 修改时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateAt;

    /**
     * 创建人名称
     */
    @TableField("create_by_name")
    private String createByName;

    /**
     * 修改人名称
     */
    @TableField("update_by_name")
    private String updateByName;

    /**
     * 获取队列状态枚举
     */
    public QueueStatus getQueueStatusEnum() {
        if (queueStatus == null) {
            return null;
        }
        return QueueStatus.valueOf(queueStatus);
    }

    /**
     * 设置队列状态枚举
     */
    public void setQueueStatusEnum(QueueStatus status) {
        this.queueStatus = status != null ? status.name() : null;
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return retryCount != null && maxRetry != null && retryCount < maxRetry;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (retryCount == null) {
            retryCount = 1;
        } else {
            retryCount++;
        }
    }

    /**
     * 检查是否达到最大重试次数
     */
    public boolean isMaxRetryReached() {
        return retryCount != null && maxRetry != null && retryCount >= maxRetry;
    }

    /**
     * 计算下次重试时间（指数退避策略）
     */
    public LocalDateTime calculateNextRetryTime() {
        if (retryCount == null || retryCount == 0) {
            return LocalDateTime.now().plusMinutes(5); // 首次重试5分钟后
        }
        // 指数退避：5分钟 * 重试次数
        long delayMinutes = 5L * retryCount;
        return LocalDateTime.now().plusMinutes(delayMinutes);
    }
}