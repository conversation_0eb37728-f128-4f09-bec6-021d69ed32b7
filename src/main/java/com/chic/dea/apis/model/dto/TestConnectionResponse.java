package com.chic.dea.apis.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 测试连接响应DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestConnectionResponse {

    /**
     * 连接是否成功
     */
    private Boolean success;

    /**
     * 错误信息(连接失败时)
     */
    private String errorMessage;

    /**
     * 连接耗时(毫秒)
     */
    private Long duration;

    /**
     * 测试时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime testTime;

    /**
     * 数据库版本信息
     */
    private String databaseVersion;

    /**
     * 数据库产品名称
     */
    private String databaseProductName;

    /**
     * 创建成功的响应
     */
    public static TestConnectionResponse success(long duration, String databaseVersion, String databaseProductName) {
        return TestConnectionResponse.builder()
            .success(true)
            .duration(duration)
            .testTime(LocalDateTime.now())
            .databaseVersion(databaseVersion)
            .databaseProductName(databaseProductName)
            .build();
    }

    /**
     * 创建失败的响应
     */
    public static TestConnectionResponse failure(String errorMessage, long duration) {
        return TestConnectionResponse.builder()
            .success(false)
            .errorMessage(errorMessage)
            .duration(duration)
            .testTime(LocalDateTime.now())
            .build();
    }
}