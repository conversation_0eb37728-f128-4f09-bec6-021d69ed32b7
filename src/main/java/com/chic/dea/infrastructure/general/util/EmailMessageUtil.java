package com.chic.dea.infrastructure.general.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chic.commons.constant.CommonConstant;
import com.chic.commons.util.RestTemplateUtils;
import com.chic.dea.infrastructure.general.constants.Constants;
import java.io.File;
import java.text.MessageFormat;
import java.util.List;
import java.util.Properties;
import javax.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;


/**
 *
 * 功能描述: 邮件模块工具
 *
 * @return:
 * @auther: wallace
 * @date: 2022/10/27 15:09
 */
@Service
@RefreshScope
@Slf4j
public class EmailMessageUtil {

    @Value("${sendMsg.email.mailUrl:null}")
    private String mailUrl;

    @Value("${sendMsg.email.appId:null}")
    private String appid;

    @Value("${sendMsg.email.appKey:null}")
    private String appkey;


    @Autowired
    RestTemplateUtils restTemplateUtils;

    /**
     * 组装手机邮件并发送
     * @param receiver 接收邮件地址集合
     * @param carbonCopyReceiver 抄送邮件地址集合
     * @param subject 标题
     * @param content 内容模板
     * @param array 替换参数列表
     * @return
     */
    public MoMessageResult sendMessageByArray(List<String> receiver,List<String> carbonCopyReceiver,String subject,String content,String[] array) {
        //返回结果
        MoMessageResult result;
        //验证参数是否有空值
        if (ObjectUtils.isEmpty(array) || ObjectUtils.isEmpty(receiver) || StringUtils.isEmpty(content) || StringUtils.isEmpty(subject)) {
            result = new MoMessageResult();
            result.setMessage("邮件相关项参数不允许为空！51");
            result.setCode(CommonConstant.API_FAILED);
        }
        //替换模板对应项 array[0] 替换 {0} ,array[1] 替换 {1} .....
        content = MessageFormat.format(content, array);
        //发送邮件参数封装
        EmailMessageInner moParam = new EmailMessageInner();
        moParam.setReceiver(receiver);
        moParam.setCarbonCopyReceiver(carbonCopyReceiver);
        moParam.setSubject(subject);
        moParam.setContent(content);
        //执行发送邮件
        result = this.sendEmailMessage(moParam);
        return result;
    }

    /**
     * 发送邮件请求
     *
     * @return
     * @throws Exception
     */
    public MoMessageResult sendEmailMessage(EmailMessageInner param) {
        //返回信息
        MoMessageResult result = new MoMessageResult();
        //邮件接口链接地址
            try {
                //日志入参暂存
                log.info("--------发送邮件请求----------根据模板内容发送邮件,发送标题：{},发送内容：{},入参：{}", param.getSubject(), param.getContent(), JSON.toJSONString(param));
                //发送信息
                ResponseEntity<String> responseEntity = restTemplateUtils.postToJsons(mailUrl + MessageFormat.format(
                    Constants.APP_PARAMETER, appid, appkey), param, String.class, CommonConstant.DEFAULT_CHARSET);
                log.info("--------发送邮件请求---------- 返回报文：{}", responseEntity.getBody());
                //解析返回结果
                JSONObject requestJson = JSONObject.parseObject(responseEntity.getBody());
                //是否成功 0:成功
                result.setCode(requestJson.getString("code"));
                result.setMessage(requestJson.getString("message"));
            } catch (Exception e) {
                log.error("--------发送邮件请求----------调用发送邮件服务异常：" + e.getMessage(), e);
                result.setCode(CommonConstant.API_FAILED);
                result.setMessage(e.getMessage());
            }
        //返回参数
        //设置请求是否成功
        return result;
    }

    private static JavaMailSenderImpl senderImpl = new JavaMailSenderImpl();
    private static Properties prop = new Properties();

    /**
     * 发送附件邮件
     * @param addresser
     * @param mail
     * @param content
     * @param subject
     * @param filepath
     * @param fileName
     */
    public void sendAttachmentMail(String addresser, String mail,String content,String subject,String filepath,String fileName){
        setMailProperties();
        MimeMessage mailMessage = senderImpl.createMimeMessage();
        try {
            // multipart模式 为true时发送附件 可以设置html格式
            MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage,true,"utf-8");
            // 设置收件人，寄件人 用数组发送多个邮件
            messageHelper.setTo(mail);
            messageHelper.setFrom(addresser);
            messageHelper.setSubject(subject);
            // true 表示启动HTML格式的邮件
            messageHelper.setText(content, false);
            FileSystemResource file = new FileSystemResource(new File(filepath));
            // 这里的方法调用和插入图片是不同的。
            messageHelper.addAttachment(file.getFilename(), file);
            // 发送邮件
            senderImpl.send(mailMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setMailProperties() {
        String host = "smtp.exmail.qq.com"; // 代理服务器地址
        String username = "<EMAIL>"; // 发件人
        String password = "g37V4hBLJSdCi9vg"; // 授权码
        String SMTP = "smtp";
        int PORT = 587;//587/465
        String DEFAULTENCODING = "UTF-8";
        // 设定mail server
        senderImpl.setHost(host);
        senderImpl.setProtocol(SMTP);
        senderImpl.setUsername(username);
        senderImpl.setPassword(password);
        senderImpl.setPort(PORT);
        senderImpl.setDefaultEncoding(DEFAULTENCODING);
        // 设定properties
        prop.put("mail.smtp.auth", "true");
        prop.put("mail.smtp.starttls.enable", "true");
        prop.put("mail.smtp.starttls.required", "true");
        prop.put("mail.smtp.timeout", "90000");
        //设置调试模式可以在控制台查看发送过程
        prop.put("mail.debug", "true");
        senderImpl.setJavaMailProperties(prop);
    }
}
