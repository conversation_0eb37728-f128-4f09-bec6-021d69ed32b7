package com.chic.dea.apis.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 数据源创建请求DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class DataSourceCreateRequest {

    /**
     * 数据源名称
     */
    @NotBlank(message = "数据源名称不能为空")
    private String name;

    /**
     * 数据源类型
     */
    @NotBlank(message = "数据源类型不能为空")
    private String type;

    /**
     * 主机地址
     */
    @NotBlank(message = "主机地址不能为空")
    private String host;

    /**
     * 端口号
     */
    @NotNull(message = "端口号不能为空")
    @Min(value = 1, message = "端口号不能小于1")
    @Max(value = 65535, message = "端口号不能大于65535")
    private Integer port;

    /**
     * 数据库名
     */
    @NotBlank(message = "数据库名不能为空")
    private String databaseName;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 其他配置(JSON格式)
     */
    private String configJson;

    /**
     * 状态:1启用,0禁用
     */
    private Integer status = 1;
}