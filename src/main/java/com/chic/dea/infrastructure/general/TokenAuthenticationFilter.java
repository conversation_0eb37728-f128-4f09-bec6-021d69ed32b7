package com.chic.dea.infrastructure.general;

import com.chic.commons.util.StringUtils;
import com.chic.dea.apis.model.dto.UserDTO;
import com.chic.dea.domain.service.CustomAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;


/**
 * <AUTHOR>
 * @classname TokenAuthenticationFilter
 * @description TODO
 * @date 2025/1/13 14:52
 */
@Slf4j
@Component
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    public static final String LOGIN_PATH = "/df/api/auth/login";
    public static final String REFRESH_TOKEN_PATH = "/df/api/auth/refresh/token";
    public static final String TOKEN = "token";
    @Autowired
    private CustomAuthService customAuthService;
        
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        String servletPath = request.getServletPath();
        if (servletPath.equals(REFRESH_TOKEN_PATH)) {
            log.info("请求刷新token接口，token={}",token);
            final String finalToken = token;
            request = new HttpServletRequestWrapper(request) {
                @Override
                public String getParameter(String name) {
                    if (TOKEN.equals(name)) {
                        return finalToken;
                    }
                    return super.getParameter(name);
                }

                @Override
                public Map<String, String[]> getParameterMap() {
                    Map<String, String[]> map = new HashMap<>(super.getParameterMap());
                    map.put(TOKEN, new String[]{finalToken});
                    return map;
                }

                @Override
                public Enumeration<String> getParameterNames() {
                    List<String> names = Collections.list(super.getParameterNames());
                    names.add(TOKEN);
                    return Collections.enumeration(names);
                }

                @Override
                public String[] getParameterValues(String name) {
                    if (TOKEN.equals(name)) {
                        return new String[]{finalToken};
                    }
                    return super.getParameterValues(name);
                }
            };
        }else{
            if (!servletPath.equals(LOGIN_PATH) && StringUtils.isNotBlank(token)) {
                UserDTO userDTO = customAuthService.selectUser(token);
                if (userDTO != null) {
                    userDTO.setToken(token);
                    setSecurityContext(userDTO);
                } else {
                    response.setStatus(HttpStatus.UNAUTHORIZED.value());
                    response.getWriter().write("Token expired");
                    return;
                }
            }
        }
        filterChain.doFilter(request, response);
    }

    private void setSecurityContext(UserDTO userDTO) {
        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(userDTO, null, new ArrayList<>());
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }
}
