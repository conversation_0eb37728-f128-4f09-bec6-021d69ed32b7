# 数据源模块使用说明

## 概述

数据源模块是提数审计系统的核心组件之一，负责管理系统中所有可用的数据源，支持多种数据库类型的连接配置、状态监控和连接测试等功能。

## 功能特性

- **多数据库支持**: 支持 MySQL、Oracle、PostgreSQL、SQL Server、DB2、ClickHouse 等主流数据库
- **连接管理**: 提供数据源的增删改查功能
- **连接测试**: 实时测试数据源连接状态
- **状态监控**: 监控数据源的启用/禁用状态
- **安全存储**: 密码加密存储，保障数据安全
- **类型验证**: 支持数据源类型和参数的完整性验证

## 模块结构

```
数据源模块/
├── 实体层 (Entity)
│   └── DataSource.java                    # 数据源实体类
├── 数据访问层 (Repository)
│   └── DataSourceMapper.java              # 数据源Mapper接口
├── 业务逻辑层 (Service)
│   ├── DataSourceService.java             # 数据源服务接口
│   └── impl/DataSourceServiceImpl.java    # 数据源服务实现
├── 控制器层 (Controller)
│   ├── DataSourceController.java          # 数据源管理接口
│   └── DataSourceTypeController.java      # 数据源类型接口
├── 数据传输对象 (DTO/VO)
│   ├── dto/DataSourceCreateRequest.java   # 创建请求DTO
│   ├── dto/DataSourceUpdateRequest.java   # 更新请求DTO
│   ├── dto/DataSourceType.java            # 数据源类型枚举
│   ├── dto/TestConnectionResponse.java    # 连接测试响应DTO
│   └── vo/DataSourceVO.java               # 数据源视图对象
└── 工具类 (Utility)
    └── DataSourceConnectionUtil.java      # 数据源连接工具类
```

## API接口说明

### 1. 数据源管理接口

#### 获取数据源列表
```http
GET /api/datasources?enabled=true
```
**参数:**
- `enabled` (可选): 是否只获取启用状态的数据源

**响应:**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "MySQL测试数据源",
      "type": "MYSQL",
      "typeName": "MySQL数据库",
      "host": "localhost",
      "port": 3306,
      "databaseName": "test_db",
      "username": "root",
      "status": 1,
      "statusName": "启用",
      "createAt": "2024-12-09 10:00:00",
      "createByName": "系统管理员"
    }
  ],
  "timestamp": 1702087200000
}
```

#### 获取数据源详情
```http
GET /api/datasources/{id}
```

#### 创建数据源
```http
POST /api/datasources
Content-Type: application/json

{
  "name": "MySQL生产数据源",
  "type": "MYSQL",
  "host": "prod-mysql.example.com",
  "port": 3306,
  "databaseName": "production_db",
  "username": "prod_user",
  "password": "secure_password",
  "status": 1
}
```

#### 更新数据源
```http
PUT /api/datasources/{id}
Content-Type: application/json

{
  "name": "MySQL生产数据源(更新)",
  "host": "new-prod-mysql.example.com",
  "status": 0
}
```

#### 删除数据源
```http
DELETE /api/datasources/{id}
```

#### 测试数据源连接
```http
POST /api/datasources/{id}/test
```

**响应:**
```json
{
  "code": 200,
  "message": "连接测试成功",
  "data": {
    "success": true,
    "duration": 120,
    "testTime": "2024-12-09 10:30:00",
    "databaseVersion": "8.0.32",
    "databaseProductName": "MySQL"
  },
  "timestamp": 1702089000000
}
```

### 2. 数据源类型接口

#### 获取支持的数据源类型
```http
GET /api/datasource-types
```

**响应:**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "code": "MYSQL",
      "name": "MySQL数据库",
      "driverClassName": "com.mysql.cj.jdbc.Driver",
      "defaultPort": 3306
    },
    {
      "code": "ORACLE",
      "name": "Oracle数据库",
      "driverClassName": "oracle.jdbc.driver.OracleDriver",
      "defaultPort": 1521
    }
  ],
  "timestamp": 1702087200000
}
```

## 数据源类型支持

| 数据库类型 | 代码 | 默认端口 | 驱动类 |
|-----------|------|----------|--------|
| MySQL | MYSQL | 3306 | com.mysql.cj.jdbc.Driver |
| Oracle | ORACLE | 1521 | oracle.jdbc.driver.OracleDriver |
| PostgreSQL | POSTGRESQL | 5432 | org.postgresql.Driver |
| SQL Server | SQLSERVER | 1433 | com.microsoft.sqlserver.jdbc.SQLServerDriver |
| IBM DB2 | DB2 | 50000 | com.ibm.db2.jcc.DB2Driver |
| ClickHouse | CLICKHOUSE | 8123 | ru.yandex.clickhouse.ClickHouseDriver |

## 数据库表结构

```sql
CREATE TABLE data_source (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    type VARCHAR(50) NOT NULL COMMENT '数据源类型',
    host VARCHAR(200) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口号',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '加密后的密码',
    config_json TEXT COMMENT '其他配置(JSON格式)',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_by VARCHAR(50) NOT NULL COMMENT '创建人',
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '修改人',
    update_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    create_by_name VARCHAR(50) COMMENT '创建人名称',
    update_by_name VARCHAR(50) COMMENT '修改人名称'
);
```

## 安全机制

### 密码加密
- 数据源密码使用Base64编码存储(开发阶段)
- 生产环境建议使用AES等强加密算法
- 密码在传输过程中不会明文返回

### 权限控制
- 所有接口都需要通过统一认证
- 基于角色的访问控制(RBAC)
- 操作日志完整记录

### 参数验证
- 创建/更新时进行完整的参数校验
- 数据源类型验证
- 连接参数格式验证

## 使用示例

### Java代码示例

```java
@Autowired
private DataSourceService dataSourceService;

// 创建数据源
DataSourceCreateRequest request = new DataSourceCreateRequest();
request.setName("测试数据源");
request.setType("MYSQL");
request.setHost("localhost");
request.setPort(3306);
request.setDatabaseName("test_db");
request.setUsername("root");
request.setPassword("password");

Long dataSourceId = dataSourceService.createDataSource(request);

// 测试连接
TestConnectionResponse response = dataSourceService.testConnection(dataSourceId);
if (response.getSuccess()) {
    System.out.println("连接测试成功，耗时: " + response.getDuration() + "ms");
} else {
    System.err.println("连接测试失败: " + response.getErrorMessage());
}

// 获取数据源实体(用于后续数据提取)
DataSource dataSource = dataSourceService.getDataSourceEntityById(dataSourceId);
Connection conn = DataSourceConnectionUtil.getConnection(dataSource);
```

## 注意事项

1. **连接池管理**: 数据源连接使用完毕后要及时关闭
2. **密码安全**: 生产环境必须使用强加密算法存储密码
3. **网络安全**: 确保数据库服务器网络安全配置
4. **权限最小化**: 数据源用户只分配必要的数据库权限
5. **监控告警**: 定期检查数据源连接状态，设置告警机制

## 故障排查

### 常见问题

1. **连接超时**
   - 检查网络连通性
   - 验证防火墙配置
   - 确认数据库服务状态

2. **认证失败**
   - 验证用户名密码正确性
   - 检查数据库用户权限
   - 确认密码是否正确解密

3. **驱动不支持**
   - 确认数据库类型配置正确
   - 检查相关数据库驱动是否已添加到项目依赖

4. **字符编码问题**
   - 确保数据库和应用程序使用相同的字符编码
   - MySQL建议使用utf8mb4字符集

### 日志查看

```bash
# 查看数据源相关日志
grep "DataSource" logs/application.log

# 查看连接测试日志
grep "连接测试" logs/application.log
```

## 后续扩展

1. **连接池优化**: 集成Druid或HikariCP连接池
2. **监控增强**: 添加连接状态实时监控
3. **缓存机制**: 对频繁查询的数据源信息进行缓存
4. **审计日志**: 详细记录数据源的所有操作历史
5. **批量操作**: 支持数据源的批量导入导出功能