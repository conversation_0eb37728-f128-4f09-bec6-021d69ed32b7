package com.chic.dea.apis.controller;

import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.dea.apis.model.dto.DataSourceCreateRequest;
import com.chic.dea.apis.model.dto.DataSourceUpdateRequest;
import com.chic.dea.apis.model.dto.TestConnectionResponse;
import com.chic.dea.apis.model.vo.DataSourceVO;
import com.chic.dea.domain.service.DataSourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据源管理控制器
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@RestController
@RequestMapping("/api/datasources")
@RequiredArgsConstructor
public class DataSourceController {

    private final DataSourceService dataSourceService;

    /**
     * 获取所有数据源列表
     * 
     * @return 数据源列表
     */
    @GetMapping
    public Result<List<DataSourceVO>> getDataSources(@RequestParam(required = false) Boolean enabled) {
        try {
            List<DataSourceVO> dataSources;
            if (Boolean.TRUE.equals(enabled)) {
                dataSources = dataSourceService.getEnabledDataSources();
            } else {
                dataSources = dataSourceService.getAllDataSources();
            }
            return Result.success(dataSources);
        } catch (Exception e) {
            log.error("获取数据源列表失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "获取数据源列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据ID获取数据源详情
     * 
     * @param id 数据源ID
     * @return 数据源详情
     */
    @GetMapping("/{id}")
    public Result<DataSourceVO> getDataSourceById(@PathVariable Long id) {
        try {
            DataSourceVO dataSource = dataSourceService.getDataSourceById(id);
            return Result.success(dataSource);
        } catch (IllegalArgumentException e) {
            log.warn("获取数据源详情失败: {}", e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("获取数据源详情失败，ID: {}", id, e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "获取数据源详情失败: " + e.getMessage()));
        }
    }

    /**
     * 创建数据源
     * 
     * @param request 创建请求
     * @return 创建的数据源ID
     */
    @PostMapping
    public Result<Long> createDataSource(@Valid @RequestBody DataSourceCreateRequest request) {
        try {
            Long dataSourceId = dataSourceService.createDataSource(request);
            log.info("创建数据源成功，ID: {}, 名称: {}", dataSourceId, request.getName());
            return Result.success(dataSourceId);
        } catch (IllegalArgumentException e) {
            log.warn("创建数据源参数错误: {}", e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("创建数据源失败，名称: {}", request.getName(), e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "创建数据源失败: " + e.getMessage()));
        }
    }

    /**
     * 更新数据源
     * 
     * @param id 数据源ID
     * @param request 更新请求
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public Result<Void> updateDataSource(@PathVariable Long id, 
                                             @Valid @RequestBody DataSourceUpdateRequest request) {
        try {
            dataSourceService.updateDataSource(id, request);
            log.info("更新数据源成功，ID: {}", id);
            return Result.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("更新数据源参数错误，ID: {}, 错误: {}", id, e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("更新数据源失败，ID: {}", id, e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "更新数据源失败: " + e.getMessage()));
        }
    }

    /**
     * 删除数据源
     * 
     * @param id 数据源ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteDataSource(@PathVariable Long id) {
        try {
            dataSourceService.deleteDataSource(id);
            log.info("删除数据源成功，ID: {}", id);
            return Result.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("删除数据源参数错误，ID: {}, 错误: {}", id, e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("删除数据源失败，ID: {}", id, e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "删除数据源失败: " + e.getMessage()));
        }
    }

    /**
     * 测试数据源连接
     * 
     * @param id 数据源ID
     * @return 连接测试结果
     */
    @PostMapping("/{id}/test")
    public Result<TestConnectionResponse> testConnection(@PathVariable Long id) {
        try {
            TestConnectionResponse response = dataSourceService.testConnection(id);
            if (response.getSuccess()) {
                log.info("数据源连接测试成功，ID: {}, 耗时: {}ms", id, response.getDuration());
                return Result.success(response);
            } else {
                log.warn("数据源连接测试失败，ID: {}, 错误: {}", id, response.getErrorMessage());
                return Result.success(response);
            }
        } catch (IllegalArgumentException e) {
            log.warn("连接测试参数错误，ID: {}, 错误: {}", id, e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("连接测试异常，ID: {}", id, e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "连接测试异常: " + e.getMessage()));
        }
    }

    /**
     * 测试数据源配置连接
     * 
     * @param request 数据源配置
     * @return 连接测试结果
     */
    @PostMapping("/test-config")
    public Result<TestConnectionResponse> testConnectionConfig(@Valid @RequestBody DataSourceCreateRequest request) {
        try {
            TestConnectionResponse response = dataSourceService.testConnectionConfig(request);
            if (response.getSuccess()) {
                log.info("数据源配置连接测试成功，主机: {}, 耗时: {}ms", request.getHost(), response.getDuration());
                return Result.success(response);
            } else {
                log.warn("数据源配置连接测试失败，主机: {}, 错误: {}", request.getHost(), response.getErrorMessage());
                return Result.success(response);
            }
        } catch (IllegalArgumentException e) {
            log.warn("配置连接测试参数错误，主机: {}, 错误: {}", request.getHost(), e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("配置连接测试异常，主机: {}", request.getHost(), e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "配置连接测试异常: " + e.getMessage()));
        }
    }

    /**
     * 根据类型获取数据源列表
     * 
     * @param type 数据源类型
     * @return 数据源列表
     */
    @GetMapping("/type/{type}")
    public Result<List<DataSourceVO>> getDataSourcesByType(@PathVariable String type) {
        try {
            List<DataSourceVO> dataSources = dataSourceService.getDataSourcesByType(type);
            return Result.success(dataSources);
        } catch (Exception e) {
            log.error("根据类型获取数据源列表失败，类型: {}", type, e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "获取数据源列表失败: " + e.getMessage()));
        }
    }

    /**
     * 检查数据源名称是否可用
     * 
     * @param name 数据源名称
     * @param excludeId 排除的ID(用于更新时检查)
     * @return 检查结果
     */
    @GetMapping("/check-name")
    public Result<Boolean> checkNameAvailable(@RequestParam String name, 
                                                  @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = dataSourceService.existsByName(name, excludeId);
            return Result.success(!exists);
        } catch (Exception e) {
            log.error("检查数据源名称失败，名称: {}", name, e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "检查名称失败: " + e.getMessage()));
        }
    }
}