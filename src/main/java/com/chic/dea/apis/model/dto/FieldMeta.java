package com.chic.dea.apis.model.dto;

/**
 * 字段元数据实体类
 * 用于表示SQL解析结果中字段的完整追踪信息，采用链表结构追踪字段传递路径
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
public class FieldMeta {

    /**
     * 字段名
     */
    private String fieldName;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 别名的唯一hash值（用于解决别名冲突问题）
     */
    private String aliasHash;
    
    /**
     * 是否为别名字段
     */
    private boolean isAlias;
    
    /**
     * 字段追踪链表的下一个节点（用于追踪字段的传递路径）
     */
    private FieldMeta next;
    
    /**
     * 字段在查询中的层级深度
     */
    private int queryLevel;
    
    /**
     * 字段的原始引用路径（如 a.fieldName）
     */
    private String originalReference;
    
    // 构造函数
    public FieldMeta() {
    }
    
    public FieldMeta(String fieldName, String tableName, String aliasHash, boolean isAlias, 
                     FieldMeta next, int queryLevel, String originalReference) {
        this.fieldName = fieldName;
        this.tableName = tableName;
        this.aliasHash = aliasHash;
        this.isAlias = isAlias;
        this.next = next;
        this.queryLevel = queryLevel;
        this.originalReference = originalReference;
    }
    
    // Getter和Setter方法
    public String getFieldName() {
        return fieldName;
    }
    
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
    
    public String getTableName() {
        return tableName;
    }
    
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
    
    public String getAliasHash() {
        return aliasHash;
    }
    
    public void setAliasHash(String aliasHash) {
        this.aliasHash = aliasHash;
    }
    
    public boolean isAlias() {
        return isAlias;
    }
    
    public void setAlias(boolean alias) {
        isAlias = alias;
    }
    
    public FieldMeta getNext() {
        return next;
    }
    
    public void setNext(FieldMeta next) {
        this.next = next;
    }
    
    public int getQueryLevel() {
        return queryLevel;
    }
    
    public void setQueryLevel(int queryLevel) {
        this.queryLevel = queryLevel;
    }
    
    public String getOriginalReference() {
        return originalReference;
    }
    
    public void setOriginalReference(String originalReference) {
        this.originalReference = originalReference;
    }
    
    // Builder模式的静态方法
    public static FieldMetaBuilder builder() {
        return new FieldMetaBuilder();
    }
    
    public static class FieldMetaBuilder {
        private String fieldName;
        private String tableName;
        private String aliasHash;
        private boolean isAlias;
        private FieldMeta next;
        private int queryLevel;
        private String originalReference;
        
        public FieldMetaBuilder fieldName(String fieldName) {
            this.fieldName = fieldName;
            return this;
        }
        
        public FieldMetaBuilder tableName(String tableName) {
            this.tableName = tableName;
            return this;
        }
        
        public FieldMetaBuilder aliasHash(String aliasHash) {
            this.aliasHash = aliasHash;
            return this;
        }
        
        public FieldMetaBuilder isAlias(boolean isAlias) {
            this.isAlias = isAlias;
            return this;
        }
        
        public FieldMetaBuilder next(FieldMeta next) {
            this.next = next;
            return this;
        }
        
        public FieldMetaBuilder queryLevel(int queryLevel) {
            this.queryLevel = queryLevel;
            return this;
        }
        
        public FieldMetaBuilder originalReference(String originalReference) {
            this.originalReference = originalReference;
            return this;
        }
        
        public FieldMeta build() {
            return new FieldMeta(fieldName, tableName, aliasHash, isAlias, next, queryLevel, originalReference);
        }
    }
    
    /**
     * 获取链表的尾节点（物理表字段）
     * @return 链表尾部的FieldMeta对象
     */
    public FieldMeta getChainTail() {
        FieldMeta current = this;
        while (current.next != null) {
            current = current.next;
        }
        return current;
    }
    
    /**
     * 获取链表的长度
     * @return 链表节点数量
     */
    public int getChainLength() {
        int length = 1;
        FieldMeta current = this;
        while (current.next != null) {
            current = current.next;
            length++;
        }
        return length;
    }
    
    /**
     * 检查是否为物理表字段（链表尾部且非别名）
     * @return true如果是物理表字段
     */
    public boolean isPhysicalTableField() {
        return next == null && !isAlias;
    }
    
    /**
     * 添加下一个节点到链表
     * @param nextNode 下一个节点
     */
    public void appendNext(FieldMeta nextNode) {
        if (this.next == null) {
            this.next = nextNode;
        } else {
            this.next.appendNext(nextNode);
        }
    }
    
    /**
     * 获取字段的完整追踪路径描述
     * @return 字段追踪路径字符串
     */
    public String getTrackingPath() {
        StringBuilder path = new StringBuilder();
        FieldMeta current = this;
        
        while (current != null) {
            if (path.length() > 0) {
                path.append(" -> ");
            }
            
            String nodeDesc = current.fieldName;
            if (current.tableName != null) {
                nodeDesc = current.tableName + "." + current.fieldName;
            }
            if (current.isAlias && current.aliasHash != null) {
                nodeDesc += "(hash:" + current.aliasHash + ")";
            }
            path.append(nodeDesc);
            
            current = current.next;
        }
        
        return path.toString();
    }
    
    @Override
    public String toString() {
        return "FieldMeta{" +
                "fieldName='" + fieldName + '\'' +
                ", tableName='" + tableName + '\'' +
                ", aliasHash='" + aliasHash + '\'' +
                ", isAlias=" + isAlias +
                ", queryLevel=" + queryLevel +
                ", originalReference='" + originalReference + '\'' +
                ", hasNext=" + (next != null) +
                '}';
    }
}
