package com.chic.dea.infrastructure.general.util;

import com.chic.dea.domain.service.EncryptionKeyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * 密码加密工具类
 * 
 * <AUTHOR>
 * @classname PasswordEncryptionUtil
 * @description 数据库密码加密解密工具，密钥从数据库字典配置中获取
 * @date 2024/12/13 16:00
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PasswordEncryptionUtil {
    private static final String ALGORITHM = "AES";

    private final EncryptionKeyService encryptionKeyService;

    /**
     * 加密
     * 
     * @param plainText 明文
     * @return 加密后的密文
     * @throws Exception 加密异常
     */
    public String encrypt(String plainText) throws Exception {
        String key = encryptionKeyService.getDatabasePasswordKey();
        if(key == null){
            throw new Exception("加密密钥为空");
        }
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encrypted = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(encrypted);
    }

    /**
     * 解密
     * 
     * @param encryptedText 加密文本
     * @return 解密后的明文
     * @throws Exception 解密异常
     */
    public String decrypt(String encryptedText) throws Exception {
        String key = encryptionKeyService.getDatabasePasswordKey();
        if(key == null){
            throw new Exception("解密密钥为空");
        }
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decodedBytes = Base64.getDecoder().decode(encryptedText);
        byte[] decrypted = cipher.doFinal(decodedBytes);
        return new String(decrypted);
    }
}
