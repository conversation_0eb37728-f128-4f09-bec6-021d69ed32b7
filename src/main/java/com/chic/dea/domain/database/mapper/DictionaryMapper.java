package com.chic.dea.domain.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chic.dea.domain.database.entity.Dictionary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 字典Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Mapper
public interface DictionaryMapper extends BaseMapper<Dictionary> {

    /**
     * 根据字典编码查询字典
     * 
     * @param code 字典编码
     * @return 字典实体
     */
    @Select("SELECT * FROM dea_dictionary WHERE code = #{code} AND status = 1 LIMIT 1")
    Dictionary findByCode(@Param("code") String code);
}