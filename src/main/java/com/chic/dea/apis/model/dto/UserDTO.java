package com.chic.dea.apis.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @classname UserDTO
 * @description TODO
 * @date 2025/1/9 21:15
 */
@NoArgsConstructor
@Data
public class UserDTO {

    private static final long serialVersionUID = 1L;

    @JsonProperty("userId")
    private String userId;
    @JsonProperty("userName")
    private String userName;
    @JsonProperty("appId")
    private String appId;
    @JsonProperty("status")
    private String status;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("loginCom")
    private String loginCom;
    @JsonProperty("email")
    private String email;
    @JsonProperty("depts")
    private List<?> depts;
    @JsonProperty("prompt")
    private Object prompt;
    @JsonProperty("token")
    private String token;
    @JsonProperty("mobile")
    private String mobile;


}
