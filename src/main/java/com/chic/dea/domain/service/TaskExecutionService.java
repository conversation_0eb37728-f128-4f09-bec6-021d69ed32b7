package com.chic.dea.domain.service;

/**
 * 任务执行服务接口
 * 负责具体的数据提取任务执行
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface TaskExecutionService {

    /**
     * 执行提数任务
     * 
     * @param taskId 任务ID
     */
    void executeExtractionTask(Long taskId);

    /**
     * 取消正在执行的任务
     * 
     * @param taskId 任务ID
     */
    void cancelTask(Long taskId);

    /**
     * 检查任务是否正在执行
     * 
     * @param taskId 任务ID
     * @return 是否正在执行
     */
    boolean isTaskExecuting(Long taskId);

    /**
     * 获取任务执行进度
     * 
     * @param taskId 任务ID
     * @return 执行进度(0-100)
     */
    Integer getTaskProgress(Long taskId);
}