# 提数审计系统设计文档

## 1. 系统概述

### 1.1 系统简介
提数审计系统是一个用于管理和执行数据提取任务的企业级应用系统，支持多数据源数据提取、合规性审查、自动化处理和安全分发等功能。

### 1.2 系统目标
- 提供统一的数据提取管理平台
- 确保数据提取过程的合规性和安全性
- 自动化数据提取和分发流程
- 提供完整的审计追踪功能

### 1.3 核心功能
- 用户登录鉴权管理
- 数据源统一管理
- OA提数任务管理
- 自动化数据提取流程
- 合规性检查和审批
- 数据安全分发

## 2. 系统架构

### 2.1 总体架构
```
┌─────────────────┐                    ┌─────────────────┐
│   前端应用层    │                    │   外部系统      │
│  (Vue/React)    │◄──────────────────►│  天权/天御/OA   │
└─────────────────┘                    └─────────────────┘
         │                                       │
         ▼                                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    应用服务层                                   │
│  ┌───────────────┐ ┌───────────────┐ ┌───────────────┐        │
│  │   用户服务    │ │   任务服务    │ │   数据服务    │        │
│  │ (User Service)│ │(Task Service) │(Data Service) │        │
│  └───────────────┘ └───────────────┘ └───────────────┘        │
└─────────────────────────────────────────────────────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    基础设施层                                   │
│  ┌───────────────┐ ┌───────────────┐ ┌───────────────┐        │
│  │   MySQL       │ │   Redis       │ │   MinIO       │        │
│  │   (主数据库)  │ │   (缓存)      │ │ (文件存储)    │        │
│  └───────────────┘ └───────────────┘ └───────────────┘        │
│  ┌───────────────┐ ┌───────────────┐ ┌───────────────┐        │
│  │  任务调度器   │ │ Spring Batch  │ │   监控告警    │        │
│  │ (Scheduler)   │ │  (批处理)     │ │  (ELK Stack)  │        │
│  └───────────────┘ └───────────────┘ └───────────────┘        │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 技术选型
- **后端框架**: Spring Boot 2.x
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.x
- **任务调度**: 数据库轮询 + 定时任务
- **文件存储**: MinIO
- **批处理**: Spring Batch
- **SQL解析**: Apache Calcite
- **前端框架**: Vue 3.x + Element Plus
- **Web服务器**: Nginx
- **监控**: ELK Stack + Prometheus

## 3. 功能模块设计

### 3.1 用户登录鉴权模块

#### 3.1.1 功能描述
集成天权系统，提供统一的用户认证和授权服务。

#### 3.1.2 主要功能
- 单点登录(SSO)集成
- JWT Token管理
- 权限控制和角色管理
- 登录日志记录

#### 3.1.3 接口设计
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest request);
    
    @PostMapping("/logout")
    public ResponseEntity<Void> logout(@RequestHeader("Authorization") String token);
    
    @GetMapping("/userinfo")
    public ResponseEntity<UserInfo> getUserInfo(@RequestHeader("Authorization") String token);
    
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refreshToken(@RequestBody RefreshTokenRequest request);
}
```

### 3.2 数据源管理模块

#### 3.2.1 功能描述
管理系统中所有可用的数据源，支持多种数据库类型。

#### 3.2.2 主要功能
- 数据源配置管理
- 连接测试和状态监控
- 数据源权限控制
- 连接池管理

#### 3.2.3 数据模型
```sql
CREATE TABLE data_source (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    type VARCHAR(50) NOT NULL COMMENT '数据源类型',
    host VARCHAR(200) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '加密后的密码',
    config_json TEXT COMMENT '其他配置',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_by VARCHAR(50) NOT NULL COMMENT '创建人',
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50) COMMENT '修改人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    create_by_name VARCHAR(50) COMMENT '创建人名称',
    update_by_name VARCHAR(50) COMMENT '修改人名称'
);
```

### 3.3 提数任务管理模块

#### 3.3.1 功能描述
管理所有提数任务的生命周期，包括创建、执行、监控和归档。

#### 3.3.2 数据模型
```sql
CREATE TABLE extraction_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    oaid VARCHAR(50) NOT NULL COMMENT 'OA ID',
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    applicant VARCHAR(50) NOT NULL COMMENT '申请人',
    applicant_email VARCHAR(100) NOT NULL COMMENT '申请人邮箱',
    applicant_phone VARCHAR(20) NOT NULL COMMENT '申请人手机',
    extractor VARCHAR(50) NOT NULL COMMENT '提数人',
    extraction_status VARCHAR(20) NOT NULL COMMENT '提取状态',
    extraction_script TEXT COMMENT '提取脚本',
    data_source_id BIGINT NOT NULL COMMENT '数据源ID',
    compliance_pdf_url VARCHAR(500) COMMENT '合规OA文件URL',
    sample_data_url VARCHAR(500) COMMENT '样例数据URL',
    result_file_url VARCHAR(500) COMMENT '结果文件URL',
    file_password VARCHAR(50) COMMENT '文件密码',
    total_records BIGINT DEFAULT 0 COMMENT '总记录数',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小',
    execution_start_time DATETIME COMMENT '执行开始时间',
    execution_end_time DATETIME COMMENT '执行结束时间',
    error_message TEXT COMMENT '错误信息',
    is_archived TINYINT DEFAULT 0 COMMENT '是否归档',
    create_by VARCHAR(50) NOT NULL COMMENT '创建人',
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50) COMMENT '修改人',
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by_name VARCHAR(50) COMMENT '创建人名称',
    update_by_name VARCHAR(50) COMMENT '修改人名称'
);
```

#### 3.3.3 任务状态枚举
```java
public enum ExtractionStatus {
    DRAFT("草稿"),
    PENDING_APPROVAL("待审批"),
    APPROVED("已审批"),
    EXECUTING("执行中"),
    COMPLETED("已完成"),
    FAILED("执行失败"),
    ARCHIVED("已归档");
}

public enum QueueStatus {
    PENDING("待执行"),
    PROCESSING("执行中"),
    COMPLETED("已完成"),
    FAILED("执行失败");
}
```

### 3.4 新建OA提数任务模块

#### 3.4.1 业务流程
```mermaid
flowchart TD
    A["输入OA ID"] --> B["查询OA信息"]
    B --> C["填充OA信息"]
    C --> D["选择数据源"]
    D --> E["编写SQL脚本"]
    E --> F["Apache Calcite语法校验"]
    F --> G{"语法是否正确"}
    G -->|否| H["显示错误信息"]
    H --> E
    G -->|是| I["Calcite解析SQL<br/>提取表名和字段"]
    I --> J["敏感字段检查<br/>(调用天御平台)"]
    J --> K{"是否涉及敏感字段"}
    K -->|否| L["保存配置信息"]
    K -->|是| M["提示上传合规审批PDF"]
    M --> N["上传合规文件"]
    N --> O["保存配置信息"]
    L --> P["预览数据<br/>(前100条)"]
    O --> P
    P --> Q{"数据确认"}
    Q -->|否| R["返回修改SQL"]
    R --> E
    Q -->|是| S["保存任务并加入调度队列"]
    S --> T["调度器轮询扫描"]
    T --> U["数据量统计"]
    U --> V{"数据量是否超过500万"}
    V -->|否| W["单线程处理"]
    V -->|是| X["多线程并行处理"]
    W --> Y["流式提取数据"]
    X --> Y
    Y --> Z["CSV格式写入"]
    Z --> AA["文件分割<br/>(每文件100万条)"]
    AA --> BB["Gzip压缩"]
    BB --> CC["上传到MinIO"]
    CC --> DD["发送邮件通知"]
    DD --> EE["发送短信密码"]
    EE --> FF["更新任务状态为完成"]

    style A fill:#e1f5fe
    style F fill:#fff3e0
    style S fill:#f3e5f5
    style X fill:#e8f5e8
    style FF fill:#e8f5e8
```

#### 3.4.2 服务实现
```java
@Service
public class TaskCreationService {
    
    @Autowired
    private SqlValidationService sqlValidationService;
    
    @Autowired
    private SensitiveFieldService sensitiveFieldService;
    
    public OAInfoResponse queryOAInfo(String oaId) {
        // 调用OA系统API获取OA信息
    }
    
    public ValidationResult validateSQL(String sql, Long dataSourceId) {
        // 1. 使用Apache Calcite进行SQL语法校验
        SqlValidationResult syntaxResult = sqlValidationService.validateSyntax(sql);
        if (!syntaxResult.isValid()) {
            return ValidationResult.fail("SQL语法错误: " + syntaxResult.getErrorMessage());
        }
        
        // 2. 解析SQL获取涉及的表和字段
        SqlParseResult parseResult = sqlValidationService.parseSQL(sql);
        
        // 3. 敏感字段检查(调用天御平台)
        SensitiveFieldCheckResult sensitiveResult = sensitiveFieldService.checkSensitiveFields(
            parseResult.getTables(), parseResult.getFields());
        
        return ValidationResult.builder()
            .valid(syntaxResult.isValid() && !sensitiveResult.hasSensitiveFields())
            .sensitiveFields(sensitiveResult.getSensitiveFields())
            .build();
    }
    
    public PreviewDataResponse previewData(String sql, Long dataSourceId) {
        // 执行SQL预览前100条数据
        String limitedSql = sqlValidationService.addLimitClause(sql, 100);
        return dataExtractionService.executeQuery(limitedSql, dataSourceId);
    }
    
    public void submitTask(TaskSubmissionRequest request) {
        // 保存任务并标记为待执行状态，等待调度器轮询处理
        ExtractionTask task = saveTask(request);
        // 创建执行队列记录
        TaskExecutionQueue queueItem = new TaskExecutionQueue();
        queueItem.setTaskId(task.getId());
        queueItem.setQueueStatus(QueueStatus.PENDING);
        queueItem.setNextExecuteTime(LocalDateTime.now());
        queueRepository.save(queueItem);
    }
}
```

#### 3.4.3 Apache Calcite SQL校验服务
```java
@Service
public class SqlValidationService {
    
    private final SqlParser.Config parserConfig;
    private final FrameworkConfig frameworkConfig;
    
    public SqlValidationService() {
        // 初始化Calcite配置
        this.parserConfig = SqlParser.config()
            .withLex(Lex.MYSQL)
            .withIdentifierMaxLength(256);
            
        // 创建数据库Schema信息
        SchemaPlus rootSchema = Frameworks.createRootSchema(true);
        this.frameworkConfig = Frameworks.newConfigBuilder()
            .parserConfig(parserConfig)
            .defaultSchema(rootSchema)
            .build();
    }
    
    public SqlValidationResult validateSyntax(String sql) {
        try {
            SqlParser parser = SqlParser.create(sql, parserConfig);
            SqlNode sqlNode = parser.parseQuery();
            
            // 语法校验通过
            return SqlValidationResult.success();
        } catch (SqlParseException e) {
            return SqlValidationResult.fail("SQL语法错误: " + e.getMessage());
        }
    }
    
    public SqlParseResult parseSQL(String sql) {
        try {
            SqlParser parser = SqlParser.create(sql, parserConfig);
            SqlNode sqlNode = parser.parseQuery();
            
            // 提取表名和字段名
            SqlTableFieldExtractor extractor = new SqlTableFieldExtractor();
            sqlNode.accept(extractor);
            
            return SqlParseResult.builder()
                .tables(extractor.getTables())
                .fields(extractor.getFields())
                .build();
        } catch (SqlParseException e) {
            throw new SqlValidationException("SQL解析失败", e);
        }
    }
    
    public String addLimitClause(String sql, int limit) {
        try {
            SqlParser parser = SqlParser.create(sql, parserConfig);
            SqlNode sqlNode = parser.parseQuery();
            
            if (sqlNode instanceof SqlSelect) {
                SqlSelect select = (SqlSelect) sqlNode;
                if (select.getFetch() == null) {
                    // 添加LIMIT子句
                    SqlNumericLiteral limitNode = SqlLiteral.createExactNumeric(
                        String.valueOf(limit), SqlParserPos.ZERO);
                    select = select.clone(SqlParserPos.ZERO);
                    select.setFetch(limitNode);
                    return select.toString();
                }
            }
            return sql;
        } catch (SqlParseException e) {
            // 解析失败时直接添加LIMIT
            return sql + " LIMIT " + limit;
        }
    }
}

// SQL表和字段提取器
class SqlTableFieldExtractor extends SqlBasicVisitor<Void> {
    private Set<String> tables = new HashSet<>();
    private Set<String> fields = new HashSet<>();
    
    @Override
    public Void visit(SqlIdentifier id) {
        if (id.isSimple()) {
            fields.add(id.getSimple());
        } else if (id.names.size() == 2) {
            tables.add(id.names.get(0));
            fields.add(id.names.get(1));
        }
        return null;
    }
    
    @Override
    public Void visit(SqlCall call) {
        if (call.getOperator() instanceof SqlAsOperator) {
            // 处理表别名
            SqlNode operand = call.operand(0);
            if (operand instanceof SqlIdentifier) {
                tables.add(((SqlIdentifier) operand).getSimple());
            }
        }
        return super.visit(call);
    }
    
    public Set<String> getTables() { return tables; }
    public Set<String> getFields() { return fields; }
}
```

### 3.5 后台提数流程模块

#### 3.5.1 任务调度器设计
```java
@Component
@EnableScheduling
public class TaskScheduler {
    
    @Scheduled(fixedDelay = 5000) // 每5秒轮询一次
    public void pollPendingTasks() {
        List<TaskExecutionQueue> pendingQueue = queueRepository.findByQueueStatusAndNextExecuteTimeLessThanEqual(
            QueueStatus.PENDING, LocalDateTime.now());
        
        for (TaskExecutionQueue queueItem : pendingQueue) {
            // 更新状态为处理中
            queueItem.setQueueStatus(QueueStatus.PROCESSING);
            queueRepository.save(queueItem);
            
            try {
                executeExtractionJob(queueItem.getTaskId());
                // 执行成功，标记为完成
                queueItem.setQueueStatus(QueueStatus.COMPLETED);
            } catch (Exception e) {
                // 执行失败，处理重试逻辑
                handleTaskFailure(queueItem, e);
            }
            queueRepository.save(queueItem);
        }
    }
    
    private void executeExtractionJob(Long taskId) {
        JobParameters jobParameters = new JobParametersBuilder()
            .addLong("taskId", taskId)
            .addLong("timestamp", System.currentTimeMillis())
            .toJobParameters();
        
        jobLauncher.run(extractionJob, jobParameters);
    }
    
    private void handleTaskFailure(TaskExecutionQueue queueItem, Exception e) {
        int retryCount = queueItem.getRetryCount() + 1;
        queueItem.setRetryCount(retryCount);
        
        if (retryCount >= queueItem.getMaxRetry()) {
            queueItem.setQueueStatus(QueueStatus.FAILED);
        } else {
            queueItem.setQueueStatus(QueueStatus.PENDING);
            // 设置下次重试时间（指数退避）
            queueItem.setNextExecuteTime(LocalDateTime.now().plusMinutes(retryCount * 5));
        }
    }
}
```

#### 3.5.2 Spring Batch作业设计
```java
@Configuration
public class ExtractionJobConfig {
    
    @Bean
    public Job extractionJob() {
        return jobBuilderFactory.get("extractionJob")
            .start(dataCountStep())
            .next(dataExtractionStep())
            .next(fileSplitStep())
            .next(fileCompressionStep())
            .next(fileUploadStep())
            .next(notificationStep())
            .build();
    }
    
    @Bean
    public Step dataCountStep() {
        return stepBuilderFactory.get("dataCountStep")
            .tasklet(new DataCountTasklet())
            .build();
    }
    
    @Bean
    public Step dataExtractionStep() {
        return stepBuilderFactory.get("dataExtractionStep")
            .<ExtractionRecord, ExtractionRecord>chunk(50000)
            .reader(dataReader())
            .writer(csvFileWriter())
            .build();
    }
    
    @Bean
    public ItemWriter<ExtractionRecord> csvFileWriter() {
        return new CSVFileItemWriter<ExtractionRecord>() {
            @Override
            public void write(List<? extends ExtractionRecord> items) throws Exception {
                // 使用优化的CSV写入器
                csvStreamWriter.writeRecordsToCSV(items, getCurrentOutputFile());
            }
        };
    }
}
```

#### 3.5.3 处理流程
1. **任务轮询**: 调度器定时扫描待执行任务
2. **数据量统计**: 执行COUNT查询获取总记录数
3. **分批读取**: 根据数据量确定分页策略
4. **文件生成**: 将数据写入Excel或CSV文件
5. **文件分割**: 超过限制时拆分成多个文件
6. **压缩打包**: 生成ZIP压缩包
7. **文件上传**: 上传到MinIO存储
8. **通知发送**: 邮件发送下载链接，短信发送密码

#### 3.5.4 千万级数据处理优化策略
```java
@Component
public class BigDataProcessingStrategy {
    
    // 千万级数据处理配置
    private static final int MAX_RECORDS_PER_BATCH = 50000;  // 每批5万条
    private static final int MAX_RECORDS_PER_FILE = 1000000; // 每个文件100万条
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    private static final int PARALLEL_WORKERS = 4; // 并行worker数量
    
    public BigDataProcessingPlan createProcessingPlan(long totalRecords) {
        BigDataProcessingPlan plan = new BigDataProcessingPlan();
        
        // 计算分批策略
        int batchCount = (int) Math.ceil((double) totalRecords / MAX_RECORDS_PER_BATCH);
        int fileCount = (int) Math.ceil((double) totalRecords / MAX_RECORDS_PER_FILE);
        
        plan.setTotalRecords(totalRecords);
        plan.setBatchCount(batchCount);
        plan.setRecordsPerBatch(MAX_RECORDS_PER_BATCH);
        plan.setFileCount(fileCount);
        plan.setRecordsPerFile(MAX_RECORDS_PER_FILE);
        plan.setParallelWorkers(Math.min(PARALLEL_WORKERS, batchCount));
        plan.setOutputFormat(OutputFormat.CSV); // 使用CSV格式
        
        // 内存优化策略
        if (totalRecords > 5000000) { // 超过500万条
            plan.setUseStreamingMode(true);
            plan.setMemoryOptimized(true);
            plan.setCompressOutput(true);
        }
        
        return plan;
    }
    
    /**
     * 流式处理大数据量提取
     */
    public void processLargeDataset(ExtractionTask task, BigDataProcessingPlan plan) {
        try {
            // 1. 创建并行处理线程池
            ExecutorService executorService = Executors.newFixedThreadPool(plan.getParallelWorkers());
            
            // 2. 分片处理
            List<CompletableFuture<String>> futures = new ArrayList<>();
            
            for (int i = 0; i < plan.getBatchCount(); i++) {
                final int batchIndex = i;
                final long offset = (long) batchIndex * plan.getRecordsPerBatch();
                final int limit = plan.getRecordsPerBatch();
                
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    return processBatch(task, offset, limit, batchIndex);
                }, executorService);
                
                futures.add(future);
            }
            
            // 3. 等待所有批次完成并收集文件路径
            List<String> filePaths = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
            
            // 4. 合并和压缩文件
            String finalFilePath = mergeAndCompressFiles(filePaths, task.getId());
            
            // 5. 更新任务状态
            updateTaskResult(task, finalFilePath, plan.getTotalRecords());
            
            executorService.shutdown();
            
        } catch (Exception e) {
            handleProcessingError(task, e);
        }
    }
    
    private String processBatch(ExtractionTask task, long offset, int limit, int batchIndex) {
        String batchSql = buildPaginatedSQL(task.getExtractionScript(), offset, limit);
        String fileName = String.format("batch_%d_%d.csv", task.getId(), batchIndex);
        
        // 使用流式CSV写入
        try (CSVWriter writer = new CSVWriter(new FileWriter(fileName))) {
            executeAndWriteToCSV(batchSql, task.getDataSourceId(), writer);
        } catch (Exception e) {
            throw new BatchProcessingException("批次处理失败", e);
        }
        
        return fileName;
    }
}
```

#### 3.5.5 CSV流式写入优化
```java
@Component
public class CSVStreamWriter {
    
    private static final int BUFFER_SIZE = 8192;
    private static final String CHARSET = "UTF-8";
    
    public void writeDataToCSV(String sql, Long dataSourceId, String filePath) {
        try (Connection conn = getConnection(dataSourceId);
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery();
             FileOutputStream fos = new FileOutputStream(filePath);
             OutputStreamWriter osw = new OutputStreamWriter(fos, CHARSET);
             BufferedWriter bw = new BufferedWriter(osw, BUFFER_SIZE);
             CSVWriter csvWriter = new CSVWriter(bw)) {
            
            // 设置MySQL流式查询
            stmt.setFetchSize(Integer.MIN_VALUE);
            
            // 写入CSV头部
            ResultSetMetaData metaData = rs.getMetaData();
            String[] headers = new String[metaData.getColumnCount()];
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                headers[i-1] = metaData.getColumnName(i);
            }
            csvWriter.writeNext(headers);
            
            // 流式写入数据
            int recordCount = 0;
            while (rs.next()) {
                String[] record = new String[metaData.getColumnCount()];
                for (int i = 1; i <= metaData.getColumnCount(); i++) {
                    Object value = rs.getObject(i);
                    record[i-1] = value != null ? value.toString() : "";
                }
                csvWriter.writeNext(record);
                
                // 每处理1万条记录刷新一次缓冲区
                if (++recordCount % 10000 == 0) {
                    csvWriter.flush();
                }
            }
            
        } catch (Exception e) {
            throw new DataExtractionException("CSV写入失败", e);
        }
    }
}
```

### 3.6 编辑提数任务模块

#### 3.6.1 业务规则
- OA信息不允许修改（OAID、标题、申请人等）
- 可修改数据源配置
- 可修改提数脚本
- 修改后需要重新进行合规性检查

#### 3.6.2 服务实现
```java
@Service
public class TaskEditService {
    
    public void updateTask(Long taskId, TaskUpdateRequest request) {
        ExtractionTask task = taskRepository.findById(taskId)
            .orElseThrow(() -> new TaskNotFoundException("任务不存在"));
        
        // 检查任务状态是否允许编辑
        if (!isEditableStatus(task.getExtractionStatus())) {
            throw new TaskNotEditableException("当前状态不允许编辑");
        }
        
        // 更新允许修改的字段
        task.setDataSourceId(request.getDataSourceId());
        task.setExtractionScript(request.getExtractionScript());
        
        // 重新进行合规性检查
        ValidationResult validation = validateSQL(request.getExtractionScript(), 
                                                request.getDataSourceId());
        if (!validation.isValid()) {
            task.setExtractionStatus(ExtractionStatus.PENDING_APPROVAL);
        }
        
        taskRepository.save(task);
    }
}
```

## 4. 数据库设计

### 4.1 核心表结构

#### 4.1.1 任务执行日志表
```sql
CREATE TABLE task_execution_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL COMMENT '任务ID',
    step_name VARCHAR(50) NOT NULL COMMENT '步骤名称',
    status VARCHAR(20) NOT NULL COMMENT '执行状态',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    error_message TEXT COMMENT '错误信息',
    execution_context TEXT COMMENT '执行上下文',
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by_name VARCHAR(50) COMMENT '创建人名称',
    INDEX idx_task_id (task_id),
    INDEX idx_start_time (start_time)
);
```

#### 4.1.2 任务调度队列表
```sql
CREATE TABLE task_execution_queue (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL COMMENT '任务ID',
    queue_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '队列状态:PENDING,PROCESSING,COMPLETED,FAILED',
    priority INT DEFAULT 0 COMMENT '优先级',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retry INT DEFAULT 3 COMMENT '最大重试次数',
    next_execute_time DATETIME NOT NULL COMMENT '下次执行时间',
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by_name VARCHAR(50) COMMENT '创建人名称',
    update_by_name VARCHAR(50) COMMENT '修改人名称',
    INDEX idx_queue_status (queue_status),
    INDEX idx_next_execute_time (next_execute_time),
    INDEX idx_task_id (task_id)
);
```

### 4.2 索引设计
```sql
-- 提数任务表索引
CREATE INDEX idx_extraction_task_oaid ON extraction_task(oaid);
CREATE INDEX idx_extraction_task_status ON extraction_task(extraction_status);
CREATE INDEX idx_extraction_task_create_at ON extraction_task(create_at);
CREATE INDEX idx_extraction_task_applicant ON extraction_task(applicant);

-- 执行日志表索引
CREATE INDEX idx_task_execution_log_task_id ON task_execution_log(task_id);
CREATE INDEX idx_task_execution_log_start_time ON task_execution_log(start_time);

-- 任务调度队列表索引
CREATE INDEX idx_task_execution_queue_status ON task_execution_queue(queue_status);
CREATE INDEX idx_task_execution_queue_next_time ON task_execution_queue(next_execute_time);
CREATE INDEX idx_task_execution_queue_task_id ON task_execution_queue(task_id);
```

## 5. 接口设计

### 5.1 任务管理接口
```java
@RestController
@RequestMapping("/api/tasks")
public class TaskController {
    
    @GetMapping
    public PageResponse<TaskListVO> getTaskList(TaskQueryRequest request);
    
    @GetMapping("/{id}")
    public ResponseEntity<TaskDetailVO> getTaskDetail(@PathVariable Long id);
    
    @PostMapping
    public ResponseEntity<Long> createTask(@RequestBody TaskCreateRequest request);
    
    @PutMapping("/{id}")
    public ResponseEntity<Void> updateTask(@PathVariable Long id, 
                                         @RequestBody TaskUpdateRequest request);
    
    @PostMapping("/{id}/execute")
    public ResponseEntity<Void> executeTask(@PathVariable Long id);
    
    @PostMapping("/{id}/archive")
    public ResponseEntity<Void> archiveTask(@PathVariable Long id);
    
    @GetMapping("/{id}/preview")
    public ResponseEntity<PreviewDataResponse> previewData(@PathVariable Long id);
    
    @PostMapping("/{id}/upload-compliance")
    public ResponseEntity<Void> uploadComplianceFile(@PathVariable Long id,
                                                    @RequestParam MultipartFile file);
}
```

### 5.2 数据源管理接口
```java
@RestController
@RequestMapping("/api/datasources")
public class DataSourceController {
    
    @GetMapping
    public ResponseEntity<List<DataSourceVO>> getDataSources();
    
    @PostMapping
    public ResponseEntity<Long> createDataSource(@RequestBody DataSourceCreateRequest request);
    
    @PutMapping("/{id}")
    public ResponseEntity<Void> updateDataSource(@PathVariable Long id,
                                                @RequestBody DataSourceUpdateRequest request);
    
    @PostMapping("/{id}/test")
    public ResponseEntity<TestConnectionResponse> testConnection(@PathVariable Long id);
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDataSource(@PathVariable Long id);
}
```

## 6. 安全设计

### 6.1 认证授权
- 集成天权系统进行统一认证
- 使用JWT Token进行无状态认证
- 实现基于角色的访问控制(RBAC)
- API接口级别的权限控制

### 6.2 数据安全
- 数据库连接信息加密存储
- 敏感数据脱敏处理
- 文件传输加密
- 审计日志完整记录

### 6.3 文件安全
- 压缩文件密码保护
- MinIO访问权限控制
- 文件下载链接时效性
- 文件自动清理机制

## 7. 监控告警

### 7.1 监控指标
- 任务执行成功率
- 任务执行时长
- 系统资源使用率
- 接口响应时间
- 错误率统计

### 7.2 告警策略
- 任务执行失败告警
- 系统资源使用率过高告警
- 接口响应时间过长告警
- 数据库连接异常告警

### 7.3 日志管理
- 结构化日志记录
- 日志等级分类
- 日志文件轮转
- 重要操作审计日志

## 8. 部署架构

### 8.1 环境配置
- 开发环境: 单机部署
- 测试环境: 双机热备
- 生产环境: 集群部署

### 8.2 项目依赖配置
```xml
<!-- pom.xml -->
<dependencies>
    <!-- Spring Boot Starters -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    
    <!-- Spring Batch -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-batch</artifactId>
    </dependency>
    
    <!-- Apache Calcite SQL解析 -->
    <dependency>
        <groupId>org.apache.calcite</groupId>
        <artifactId>calcite-core</artifactId>
        <version>1.35.0</version>
    </dependency>
    
    <!-- CSV处理 -->
    <dependency>
        <groupId>com.opencsv</groupId>
        <artifactId>opencsv</artifactId>
        <version>5.8</version>
    </dependency>
    
    <!-- MinIO客户端 -->
    <dependency>
        <groupId>io.minio</groupId>
        <artifactId>minio</artifactId>
        <version>8.5.5</version>
    </dependency>
    
    <!-- 数据库驱动 -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
    </dependency>
    
    <!-- 其他工具库 -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>1.21</version>
    </dependency>
</dependencies>
```

### 8.3 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  extraction-system:
    image: extraction-system:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - JVM_OPTS=-Xmx8g -Xms8g -XX:+UseG1GC
    depends_on:
      - mysql
      - redis
      - minio
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: extraction_system
      MYSQL_ROOT_PASSWORD: password
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:6.2
    volumes:
      - redis_data:/data
  
  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data

volumes:
  mysql_data:
  redis_data:
  minio_data:
```

## 9. 性能优化

### 9.1 数据库优化
- 合理的索引设计
- SQL查询优化
- 读写分离
- 连接池配置优化

### 9.2 缓存策略
- Redis缓存热点数据
- 查询结果缓存
- 页面静态资源缓存
- CDN加速

### 9.3 异步处理
- 数据库轮询异步处理
- Spring Batch批处理
- 异步文件上传
- 邮件短信异步发送

### 9.4 千万级数据处理优化方案

#### 9.4.1 数据库查询优化
```sql
-- 1. 使用覆盖索引减少回表
CREATE INDEX idx_covering ON large_table(id, field1, field2, field3);

-- 2. 分区表设计（按时间分区）
CREATE TABLE large_table (
    id BIGINT,
    create_at DATETIME,
    data_field VARCHAR(500)
) PARTITION BY RANGE (YEAR(create_at)) (
    PARTITION p2022 VALUES LESS THAN (2023),
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025)
);

-- 3. 流式查询设置
SET SESSION net_write_timeout = 3600;
SET SESSION max_execution_time = 3600000;
```

#### 9.4.2 内存优化策略
| 优化项目 | 配置方案 | 说明 |
|---------|----------|------|
| JVM堆内存 | -Xmx8g -Xms8g | 固定堆内存避免动态调整 |
| 新生代配置 | -Xmn2g | 新生代占堆内存25% |
| GC优化 | -XX:+UseG1GC -XX:MaxGCPauseMillis=200 | 使用G1垃圾收集器 |
| 直接内存 | -XX:MaxDirectMemorySize=4g | 设置直接内存上限 |
| 元数据区 | -XX:MetaspaceSize=256m | 设置元数据区大小 |

#### 9.4.3 并发处理配置
```yaml
# application.yml
big-data-processing:
  # 并行worker数量（根据CPU核心数调整）
  parallel-workers: 8
  # 每批处理记录数
  batch-size: 50000
  # 数据库连接池配置
  datasource:
    maximum-pool-size: 20
    minimum-idle: 5
    connection-timeout: 30000
    idle-timeout: 600000
    max-lifetime: 1800000
  # CSV写入缓冲区大小
  csv:
    buffer-size: 16384
    flush-interval: 10000
```

#### 9.4.4 文件处理优化
| 处理阶段 | 优化策略 | 具体实现 |
|---------|----------|----------|
| 数据提取 | 流式查询 | `stmt.setFetchSize(Integer.MIN_VALUE)` |
| 文件写入 | 缓冲写入 | `BufferedWriter(8KB缓冲区)` |
| 文件压缩 | 多线程压缩 | `Gzip + 并行压缩` |
| 文件分割 | 按大小分割 | `每个文件最大100MB` |
| 临时文件清理 | 自动清理 | `定时清理超过24小时的临时文件` |

#### 9.4.5 网络传输优化
```java
// MinIO上传优化配置
MinioClient minioClient = MinioClient.builder()
    .endpoint("http://minio:9000")
    .credentials("access-key", "secret-key")
    .build();

// 分片上传大文件
PutObjectArgs putObjectArgs = PutObjectArgs.builder()
    .bucket("extraction-files")
    .object(fileName)
    .stream(inputStream, fileSize, -1)
    .partSize(10 * 1024 * 1024) // 10MB分片
    .build();
```

#### 9.4.6 监控告警配置
```yaml
# 大数据处理监控指标
monitoring:
  metrics:
    - name: "extraction_task_duration"
      description: "提数任务执行时长"
      threshold: 3600 # 1小时
    - name: "memory_usage_percentage"
      description: "内存使用率"
      threshold: 85 # 85%
    - name: "disk_space_usage"
      description: "磁盘空间使用率"
      threshold: 80 # 80%
    - name: "database_connection_pool_usage"
      description: "数据库连接池使用率"
      threshold: 90 # 90%
```

#### 9.4.7 故障恢复机制
| 故障类型 | 检测方式 | 恢复策略 |
|---------|----------|----------|
| 内存溢出 | JVM监控 | 自动重启 + 减少批次大小 |
| 数据库连接超时 | 连接池监控 | 重试机制 + 连接池重置 |
| 磁盘空间不足 | 磁盘监控 | 暂停任务 + 清理临时文件 |
| 网络传输失败 | 上传监控 | 断点续传 + 重试机制 |
| SQL执行超时 | 查询监控 | 增加超时时间 + 分片重试 |

#### 9.4.8 CSV vs Excel性能对比
| 对比项 | CSV格式 | Excel格式 | 性能提升 |
|--------|---------|-----------|----------|
| 文件大小 | 100MB | 150MB | 33%减少 |
| 写入速度 | 10万条/秒 | 3万条/秒 | 233%提升 |
| 内存占用 | 50MB | 200MB | 75%减少 |
| 读取速度 | 15万条/秒 | 5万条/秒 | 200%提升 |
| 跨平台兼容性 | 优秀 | 良好 | - |
| 压缩比 | 80% | 60% | 25%提升 |

#### 9.4.9 千万级数据处理最佳实践总结
```yaml
# 推荐配置 - 千万级数据处理
recommended-config:
  data-volume: "10,000,000+ records"
  output-format: "CSV"
  sql-parser: "Apache Calcite"
  processing-strategy:
    parallel-workers: 8
    batch-size: 50000
    records-per-file: 1000000
    streaming-mode: true
    memory-optimized: true
  performance-targets:
    processing-speed: "100万条/分钟"
    memory-usage: "<8GB"
    file-generation: "<2小时"
    success-rate: ">99.5%"
```

## 10. 扩展性设计

### 10.1 插件化架构
- 数据源类型插件化
- 文件格式插件化
- 通知方式插件化
- 合规检查规则插件化

### 10.2 微服务拆分
- 用户服务独立部署
- 任务服务独立部署
- 数据服务独立部署
- 通知服务独立部署

### 10.3 配置外部化
- 数据源配置外部化
- 业务规则配置化
- 系统参数配置化
- 多环境配置管理

---

**文档版本**: v1.1  
**创建时间**: 2024年12月  
**更新时间**: 2024年12月  
**文档状态**: 设计阶段  
**更新内容**: 新增Apache Calcite SQL解析、千万级数据处理优化方案、CSV输出格式  
**维护人员**: 系统架构团队 