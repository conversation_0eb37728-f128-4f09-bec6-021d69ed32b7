package com.chic.dea.apis.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 表字段实体类
 * 用于表示SQL解析结果中表和对应字段的关联关系
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TableFieldEntity {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 该表涉及的字段列表
     */
    private List<String> fields;
}
