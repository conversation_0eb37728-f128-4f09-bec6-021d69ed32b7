package com.chic.dea.apis.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 数据源更新请求DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class DataSourceUpdateRequest {

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源类型
     */
    private String type;

    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口号
     */
    @Min(value = 1, message = "端口号不能小于1")
    @Max(value = 65535, message = "端口号不能大于65535")
    private Integer port;

    /**
     * 数据库名
     */
    private String databaseName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 其他配置(JSON格式)
     */
    private String configJson;

    /**
     * 状态:1启用,0禁用
     */
    private Integer status;
}