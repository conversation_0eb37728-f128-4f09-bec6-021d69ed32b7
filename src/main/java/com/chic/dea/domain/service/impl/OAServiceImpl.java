package com.chic.dea.domain.service.impl;

import com.chic.dea.apis.model.dto.OAInfoResponse;
import com.chic.dea.domain.database.entity.OAInfo;
import com.chic.dea.domain.database.mapper.OAInfoMapper;
import com.chic.dea.domain.service.OAService;
import com.chic.dea.infrastructure.general.config.OATableConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * OA信息服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OAServiceImpl implements OAService {

    private final OAInfoMapper oaInfoMapper;
    private final OATableConfig oaTableConfig;

    @Override
    public OAInfoResponse getOAInfoById(String oaId) {
        try {
            log.debug("开始查询OA信息，ID: {}", oaId);
            
            // 参数校验
            if (!StringUtils.hasText(oaId)) {
                log.warn("OA ID为空");
                return OAInfoResponse.fail("OA ID不能为空");
            }
            
            // 查询OA信息
            OAInfo oaInfo = oaInfoMapper.findByLcid(oaTableConfig.getMainTable(), oaId);
            
            if (oaInfo == null) {
                log.warn("未找到OA信息，ID: {}", oaId);
                return OAInfoResponse.fail("未找到对应的OA信息");
            }
            
            log.debug("查询OA信息成功，ID: {}, 标题: {}", oaId, oaInfo.getBt());
            
            // 转换为响应DTO
            return OAInfoResponse.success(
                oaInfo.getLcid(),
                oaInfo.getBt(),
                oaInfo.getYx(),
                oaInfo.getSjhm(),
                oaInfo.getBtqz(),
                oaInfo.getSqrqz(),
                oaInfo.getFqgsqz()
            );
            
        } catch (Exception e) {
            log.error("查询OA信息异常，ID: {}", oaId, e);
            return OAInfoResponse.fail("查询OA信息失败: " + e.getMessage());
        }
    }
}