package com.chic.dea.infrastructure.general.constants;

import java.util.HashMap;

/**
 * @Description: java类作用描述
 * @Author: lizemin
 * @CreateDate: 2020-12-17$ 18:24$
 * @UpdateUser: lizemin
 * @UpdateDate: 2020-12-17$ 18:24$
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */

public class Constants {


  /**
   * 业务号码类型长度
   */
  public static final int iTypeLenth = 1;

  /**
   * 机构编码的长度
   */
  public static final int iComCodeLength = 4;

  /**
   * 单号年月（yyyyMM）长度(注意：可设置为6或4，如果设置为4则为yyMM)
   */
  public static final int iYearMonthLength = 4;

  /**
   * 序列号长度
   */
  public static final int iSerialNoLength = 10;

  /**
   * 业务号码的长度=业务号码类型+机构编码+单号年月+序列号
   */
  public static final int iIDLength = 19;


  /**
   * 是否成功 0:成功 1:失败
   */
  public static final String API_SUCCESS = "0";

  /**
   * 是否成功 0:成功 1:失败
   */
  public static final String API_FAILED = "1";

  /**
   * email 参数替换
   */
  public final static String APP_PARAMETER = "?appid={0}&appkey={1}";

  /**
   * logger 名称，异步开票日志
   */
  public static final String LOGGER_NAME_ASYNC_INVOICE = "asyncInvoice";

  /**
   * 默认编码格式.
   */
  public static final String DEFAULT_CHARSET = "UTF-8";

}
