package com.chic.dea.domain.database.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * OA信息实体类
 * 对应OA系统中的动态配置表
 * 
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
public class OAInfo {

    /**
     * OA ID (主键)
     */
    @TableId("lcid")
    private String lcid;

    /**
     * 标题
     */
    @TableField("bt")
    private String bt;

    /**
     * 邮箱
     */
    @TableField("yx")
    private String yx;

    /**
     * 手机号码
     */
    @TableField("sjhm")
    private String sjhm;

    /**
     * 标题名称
     */
    @TableField("btqz")
    private String btqz;

    /**
     * 申请人名称
     */
    @TableField("sqrqz")
    private String sqrqz;

    /**
     * 分公司名称
     */
    @TableField("fqgsqz")
    private String fqgsqz;
}