package com.chic.dea.infrastructure.general;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @classname CurrentUser
 * @description TODO
 * @date 2025/1/13 16:00
 */
@Target(ElementType.PARAMETER) // 注解用于方法参数
@Retention(RetentionPolicy.RUNTIME) // 注解在运行时保留
public @interface CurrentUser {
}
