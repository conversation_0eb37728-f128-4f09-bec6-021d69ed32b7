package com.chic.dea.domain.database.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据源实体类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@TableName("data_source")
public class DataSource {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据源名称
     */
    @TableField("name")
    private String name;

    /**
     * 数据源类型(MYSQL, ORACLE, POSTGRESQL等)
     */
    @TableField("type")
    private String type;

    /**
     * 主机地址
     */
    @TableField("host")
    private String host;

    /**
     * 端口号
     */
    @TableField("port")
    private Integer port;

    /**
     * 数据库名
     */
    @TableField("database_name")
    private String databaseName;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 加密后的密码
     */
    @TableField("password")
    private String password;

    /**
     * 其他配置(JSON格式)
     */
    @TableField("config_json")
    private String configJson;

    /**
     * 状态:1启用,0禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    private LocalDateTime createAt;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateAt;

    /**
     * 创建人名称
     */
    @TableField("create_by_name")
    private String createByName;

    /**
     * 修改人名称
     */
    @TableField("update_by_name")
    private String updateByName;

}