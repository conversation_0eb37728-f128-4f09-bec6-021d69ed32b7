package com.chic.dea.infrastructure.general.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * OA表名配置类
 * 
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@Component
@ConfigurationProperties(prefix = "custom.oa")
public class OATableConfig {

    /**
     * OA主表名
     */
    private String mainTable = "formtable_main_69";
}
