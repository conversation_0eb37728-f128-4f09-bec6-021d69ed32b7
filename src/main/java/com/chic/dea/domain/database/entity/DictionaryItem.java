package com.chic.dea.domain.database.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典项实体类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@TableName("dea_dictionary_item")
public class DictionaryItem {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属字典ID，外键关联字典表
     */
    @TableField("dictionary_id")
    private Long dictionaryId;

    /**
     * 字典项编码，唯一标识
     */
    @TableField("code")
    private String code;

    /**
     * 字典项名称
     */
    @TableField("name")
    private String name;

    /**
     * 字典项值
     */
    @TableField("value")
    private String value;

    /**
     * 字典项描述
     */
    @TableField("description")
    private String description;

    /**
     * 排序字段，值越小越靠前
     */
    @TableField("order_index")
    private Integer orderIndex;

    /**
     * 状态（1: 启用, 0: 停用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
}