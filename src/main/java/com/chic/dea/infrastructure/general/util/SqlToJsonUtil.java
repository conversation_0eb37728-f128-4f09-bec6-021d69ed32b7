package com.chic.dea.infrastructure.general.util;

/**
 * SQL转JSON字符串工具类
 * 主要用于将SQL语句转换为JSON格式的字符串，处理双引号和换行符的转义
 * 
 * <AUTHOR>
 * @since 2025-01-10
 */
public class SqlToJsonUtil {

    /**
     * 将SQL字符串转换为JSON格式的字符串
     * 主要处理：
     * 1. 双引号转义为 \"
     * 2. 换行符转义为 \n
     * 3. 制表符转义为 \t
     * 4. 回车符转义为 \r
     * 5. 反斜杠转义为 \\
     * 
     * @param sql 原始SQL字符串
     * @return 转义后的JSON格式字符串
     */
    public static String sqlToJsonString(String sql) {
        if (sql == null) {
            return "null";
        }
        
        return sql.replace("\\", "\\\\")  // 反斜杠必须首先转义
                 .replace("\"", "\\\"")   // 双引号转义
                 .replace("\n", "\\n")    // 换行符转义
                 .replace("\r", "\\r")    // 回车符转义
                 .replace("\t", "\\t")    // 制表符转义
                 .replace("\b", "\\b")    // 退格符转义
                 .replace("\f", "\\f");   // 换页符转义
    }

    /**
     * 将SQL字符串转换为完整的JSON格式
     * 包含外层的双引号
     * 
     * @param sql 原始SQL字符串
     * @return 完整的JSON字符串格式（包含外层双引号）
     */
    public static String sqlToCompleteJsonString(String sql) {
        if (sql == null) {
            return "null";
        }
        
        return "\"" + sqlToJsonString(sql) + "\"";
    }

    /**
     * 将SQL字符串转换为JSON对象中的值
     * 适用于构建完整的JSON对象
     * 
     * @param key JSON对象的键名
     * @param sql 原始SQL字符串
     * @return JSON对象格式的字符串
     */
    public static String sqlToJsonObject(String key, String sql) {
        if (key == null) {
            key = "sql";
        }
        
        String escapedKey = sqlToJsonString(key);
        String escapedSql = sqlToJsonString(sql);
        
        return "{\"" + escapedKey + "\":\"" + escapedSql + "\"}";
    }

    /**
     * 批量转换多个SQL语句为JSON数组格式
     * 
     * @param sqls SQL语句数组
     * @return JSON数组格式的字符串
     */
    public static String sqlArrayToJsonArray(String[] sqls) {
        if (sqls == null || sqls.length == 0) {
            return "[]";
        }
        
        StringBuilder jsonArray = new StringBuilder("[");
        for (int i = 0; i < sqls.length; i++) {
            if (i > 0) {
                jsonArray.append(",");
            }
            jsonArray.append(sqlToCompleteJsonString(sqls[i]));
        }
        jsonArray.append("]");
        
        return jsonArray.toString();
    }

    /**
     * 从JSON字符串中恢复原始SQL
     * 与sqlToJsonString方法相反的操作
     * 
     * @param jsonString 已转义的JSON字符串
     * @return 原始SQL字符串
     */
    public static String jsonStringToSql(String jsonString) {
        if (jsonString == null) {
            return null;
        }
        
        return jsonString.replace("\\\"", "\"")   // 恢复双引号
                        .replace("\\n", "\n")     // 恢复换行符
                        .replace("\\r", "\r")     // 恢复回车符
                        .replace("\\t", "\t")     // 恢复制表符
                        .replace("\\b", "\b")     // 恢复退格符
                        .replace("\\f", "\f")     // 恢复换页符
                        .replace("\\\\", "\\");   // 反斜杠必须最后恢复
    }

    /**
     * 验证转换后的JSON字符串是否有效
     * 简单的语法验证
     * 
     * @param jsonString JSON字符串
     * @return 是否为有效的JSON格式
     */
    public static boolean isValidJsonString(String jsonString) {
        if (jsonString == null) {
            return false;
        }
        
        // 简单验证：检查是否有未转义的双引号、换行符等
        String temp = jsonString;
        
        // 移除所有已正确转义的字符
        temp = temp.replace("\\\"", "")
                  .replace("\\n", "")
                  .replace("\\r", "")
                  .replace("\\t", "")
                  .replace("\\b", "")
                  .replace("\\f", "")
                  .replace("\\\\", "");
        
        // 如果还有未转义的特殊字符，则无效
        return !temp.contains("\"") && 
               !temp.contains("\n") && 
               !temp.contains("\r") && 
               !temp.contains("\t");
    }
}
