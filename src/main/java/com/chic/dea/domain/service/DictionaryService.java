package com.chic.dea.domain.service;

import com.chic.dea.apis.model.vo.DictionaryItemVO;

import java.util.List;

/**
 * 字典服务接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface DictionaryService {

    /**
     * 根据字典编码查询字典项列表
     * 
     * @param dictionaryCode 字典编码
     * @return 字典项VO列表
     */
    List<DictionaryItemVO> getDictionaryItemsByCode(String dictionaryCode);

    /**
     * 根据字典编码和字典项编码获取字典项值
     * 
     * @param dictionaryCode 字典编码
     * @param itemCode 字典项编码
     * @return 字典项值
     */
    String getDictionaryItemValue(String dictionaryCode, String itemCode);
}