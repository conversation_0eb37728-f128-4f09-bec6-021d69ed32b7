package com.chic.dea.domain.service;

import com.chic.dea.apis.model.dto.*;
import com.chic.dea.domain.database.entity.ExtractionTask;

import java.util.List;

/**
 * 任务创建服务接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface TaskCreationService {

    /**
     * 查询OA信息
     * 
     * @param oaId OA ID
     * @return OA信息响应
     */
    OAInfoResponse queryOAInfo(String oaId);

    /**
     * 校验SQL脚本
     * 
     * @param sql SQL脚本
     * @param dataSourceId 数据源ID
     * @return 校验结果
     */
    List<SensitiveFieldService.SensitiveFieldCheckResult> validateSQL(String sql, Long dataSourceId);

    /**
     * 预览数据
     * 
     * @param sql SQL脚本
     * @param dataSourceId 数据源ID
     * @return 预览数据响应
     */
    PreviewDataResponse previewData(String sql, Long dataSourceId);

    /**
     * 创建草稿任务
     * 
     * @param request 任务创建请求
     * @return 创建的任务ID
     */
    Long createDraftTask(TaskCreateRequest request);

    /**
     * 提交任务到执行队列
     * 
     * @param request 任务提交请求
     * @return 任务ID
     */
    Long submitTask(TaskSubmissionRequest request);

    /**
     * 检查OA ID是否已存在
     * 
     * @param oaId OA ID
     * @return 是否存在
     */
    boolean existsByOaId(String oaId);

    /**
     * 上传合规审批文件
     * 
     * @param taskId 任务ID
     * @param fileUrl 文件URL
     */
    void uploadComplianceFile(Long taskId, String fileUrl);

    /**
     * 生成样例数据文件
     * 
     * @param taskId 任务ID
     * @return 样例数据文件URL
     */
    String generateSampleData(Long taskId);

    /**
     * 检查任务是否可以提交
     * 
     * @param taskId 任务ID
     * @return 是否可以提交
     */
    boolean canSubmitTask(Long taskId);
}
