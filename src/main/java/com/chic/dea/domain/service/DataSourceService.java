package com.chic.dea.domain.service;

import com.chic.dea.apis.model.dto.DataSourceCreateRequest;
import com.chic.dea.apis.model.dto.DataSourceUpdateRequest;
import com.chic.dea.apis.model.dto.TestConnectionResponse;
import com.chic.dea.apis.model.vo.DataSourceVO;
import com.chic.dea.domain.database.entity.DataSource;

import java.util.List;

/**
 * 数据源服务接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface DataSourceService {

    /**
     * 获取所有数据源列表
     * 
     * @return 数据源VO列表
     */
    List<DataSourceVO> getAllDataSources();

    /**
     * 获取启用状态的数据源列表
     * 
     * @return 启用的数据源VO列表
     */
    List<DataSourceVO> getEnabledDataSources();

    /**
     * 根据ID获取数据源详情
     * 
     * @param id 数据源ID
     * @return 数据源VO
     */
    DataSourceVO getDataSourceById(Long id);

    /**
     * 根据ID获取数据源实体
     * 
     * @param id 数据源ID
     * @return 数据源实体
     */
    DataSource getDataSourceEntityById(Long id);

    /**
     * 创建数据源
     * 
     * @param request 创建请求
     * @return 创建的数据源ID
     */
    Long createDataSource(DataSourceCreateRequest request);

    /**
     * 更新数据源
     * 
     * @param id 数据源ID
     * @param request 更新请求
     */
    void updateDataSource(Long id, DataSourceUpdateRequest request);

    /**
     * 删除数据源
     * 
     * @param id 数据源ID
     */
    void deleteDataSource(Long id);

    /**
     * 测试数据源连接
     * 
     * @param id 数据源ID
     * @return 测试连接响应
     */
    TestConnectionResponse testConnection(Long id);

    /**
     * 测试数据源配置连接
     * 
     * @param request 数据源创建请求(包含连接配置)
     * @return 测试连接响应
     */
    TestConnectionResponse testConnectionConfig(DataSourceCreateRequest request);

    /**
     * 根据类型获取数据源列表
     * 
     * @param type 数据源类型
     * @return 数据源VO列表
     */
    List<DataSourceVO> getDataSourcesByType(String type);

    /**
     * 检查数据源名称是否存在
     * 
     * @param name 数据源名称
     * @param excludeId 排除的ID(用于更新时检查)
     * @return 是否存在
     */
    boolean existsByName(String name, Long excludeId);
}