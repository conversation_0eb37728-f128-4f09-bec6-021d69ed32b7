package com.chic.dea.apis.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SQL 血缘返回的表与字段聚合结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LineageTableFieldsVO {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 该表涉及的字段列表
     */
    private List<String> fieldList;
}


