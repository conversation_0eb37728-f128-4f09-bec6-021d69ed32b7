package com.chic.dea.infrastructure.general.util;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

/**
 * <AUTHOR>
 * @classname JsonUtil
 * @description TODO
 * @date 2025/1/10 13:49
 */
public class JsonUtil {


    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.convertValue(map, clazz);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
