<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chic.dea.domain.database.mapper.ExtractionTaskMapper">

    <!-- 分页查询任务列表 -->
    <select id="selectTaskPage" resultType="com.chic.dea.apis.model.vo.TaskListVO">
        SELECT 
            t.id,
            t.oaid,
            t.title,
            t.applicant,
            t.applicant_email,
            t.extractor,
            t.extraction_status,
            t.data_source_id,
            ds.name as data_source_name,
            t.total_records,
            t.file_size,
            t.execution_start_time,
            t.execution_end_time,
            t.is_archived,
            t.create_by,
            t.create_at,
            t.create_by_name,
            t.update_at,
            t.result_file_url
        FROM extraction_task t
        LEFT JOIN data_source ds ON t.data_source_id = ds.id
        <where>
            <if test="request.oaid != null and request.oaid != ''">
                AND t.oaid LIKE CONCAT('%', #{request.oaid}, '%')
            </if>
            <if test="request.title != null and request.title != ''">
                AND t.title LIKE CONCAT('%', #{request.title}, '%')
            </if>
            <if test="request.applicant != null and request.applicant != ''">
                AND t.applicant LIKE CONCAT('%', #{request.applicant}, '%')
            </if>
            <if test="request.extractor != null and request.extractor != ''">
                AND t.extractor LIKE CONCAT('%', #{request.extractor}, '%')
            </if>
            <if test="request.extractionStatus != null and request.extractionStatus != ''">
                AND t.extraction_status = #{request.extractionStatus}
            </if>
            <if test="request.dataSourceId != null">
                AND t.data_source_id = #{request.dataSourceId}
            </if>
            <if test="request.isArchived != null">
                AND t.is_archived = #{request.isArchived}
            </if>
            <if test="request.createTimeStart != null">
                AND t.create_at >= #{request.createTimeStart}
            </if>
            <if test="request.createTimeEnd != null">
                AND t.create_at &lt;= #{request.createTimeEnd}
            </if>
            <if test="request.executionTimeStart != null">
                AND t.execution_start_time >= #{request.executionTimeStart}
            </if>
            <if test="request.executionTimeEnd != null">
                AND t.execution_end_time &lt;= #{request.executionTimeEnd}
            </if>
        </where>
        ORDER BY t.create_at DESC
    </select>

    <!-- 根据状态统计任务数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT 
            extraction_status as status,
            COUNT(1) as count
        FROM extraction_task 
        WHERE is_archived = 0
        GROUP BY extraction_status
    </select>

    <!-- 统计最近7天的任务创建情况 -->
    <select id="getRecentTaskStatistics" resultType="java.util.Map">
        SELECT 
            DATE(create_at) as date,
            COUNT(1) as task_count,
            SUM(CASE WHEN extraction_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN extraction_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count
        FROM extraction_task 
        WHERE create_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(create_at)
        ORDER BY date DESC
    </select>

    <!-- 查询执行时间最长的任务 -->
    <select id="getLongestExecutionTasks" resultType="com.chic.dea.apis.model.vo.TaskListVO">
        SELECT 
            t.id,
            t.oaid,
            t.title,
            t.applicant,
            t.extractor,
            t.extraction_status,
            t.execution_start_time,
            t.execution_end_time,
            TIMESTAMPDIFF(MICROSECOND, t.execution_start_time, t.execution_end_time) / 1000 as execution_duration,
            ds.name as data_source_name
        FROM extraction_task t
        LEFT JOIN data_source ds ON t.data_source_id = ds.id
        WHERE t.extraction_status = 'COMPLETED' 
        AND t.execution_start_time IS NOT NULL 
        AND t.execution_end_time IS NOT NULL
        ORDER BY execution_duration DESC
        LIMIT #{limit}
    </select>

    <!-- 查询频繁失败的任务模式 -->
    <select id="getFrequentFailurePatterns" resultType="java.util.Map">
        SELECT 
            ds.name as data_source_name,
            COUNT(1) as failure_count,
            AVG(t.total_records) as avg_records
        FROM extraction_task t
        LEFT JOIN data_source ds ON t.data_source_id = ds.id
        WHERE t.extraction_status = 'FAILED'
        AND t.create_at >= #{startTime}
        GROUP BY t.data_source_id, ds.name
        HAVING failure_count >= #{failureThreshold}
        ORDER BY failure_count DESC
    </select>

    <!-- 查询任务成功率统计 -->
    <select id="getTaskSuccessRateStatistics" resultType="java.util.Map">
        SELECT 
            ds.name as data_source_name,
            COUNT(1) as total_tasks,
            SUM(CASE WHEN t.extraction_status = 'COMPLETED' THEN 1 ELSE 0 END) as success_tasks,
            SUM(CASE WHEN t.extraction_status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks,
            ROUND(SUM(CASE WHEN t.extraction_status = 'COMPLETED' THEN 1 ELSE 0 END) * 100.0 / COUNT(1), 2) as success_rate
        FROM extraction_task t
        LEFT JOIN data_source ds ON t.data_source_id = ds.id
        WHERE t.create_at >= #{startTime}
        AND t.create_at <= #{endTime}
        GROUP BY t.data_source_id, ds.name
        ORDER BY success_rate DESC
    </select>

    <!-- 查询大数据量任务统计 -->
    <select id="getBigDataTaskStatistics" resultType="java.util.Map">
        SELECT 
            CASE 
                WHEN total_records &lt; 100000 THEN '小于10万'
                WHEN total_records &lt; 1000000 THEN '10万-100万'
                WHEN total_records &lt; 5000000 THEN '100万-500万'
                WHEN total_records &lt; 10000000 THEN '500万-1000万'
                ELSE '超过1000万'
            END as data_scale,
            COUNT(1) as task_count,
            AVG(TIMESTAMPDIFF(MICROSECOND, execution_start_time, execution_end_time) / 1000) as avg_duration
        FROM extraction_task 
        WHERE extraction_status = 'COMPLETED'
        AND total_records > 0
        AND execution_start_time IS NOT NULL 
        AND execution_end_time IS NOT NULL
        GROUP BY 
            CASE 
                WHEN total_records &lt; 100000 THEN '小于10万'
                WHEN total_records &lt; 1000000 THEN '10万-100万'
                WHEN total_records &lt; 5000000 THEN '100万-500万'
                WHEN total_records &lt; 10000000 THEN '500万-1000万'
                ELSE '超过1000万'
            END
        ORDER BY MIN(total_records)
    </select>

</mapper>