-- 数据源表建表SQL
CREATE TABLE IF NOT EXISTS data_source (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    type VARCHAR(50) NOT NULL COMMENT '数据源类型(MYSQL,ORACLE,POSTGRESQL等)',
    host VARCHAR(200) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口号',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '加密后的密码',
    config_json TEXT COMMENT '其他配置(JSON格式)',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_by VARCHAR(50) NOT NULL COMMENT '创建人',
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '修改人',
    update_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    create_by_name VARCHAR(50) COMMENT '创建人名称',
    update_by_name VARCHAR(50) COMMENT '修改人名称',
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_create_at (create_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据源配置表';

-- 插入示例数据
INSERT INTO data_source (name, type, host, port, database_name, username, password, status, create_by, create_by_name) VALUES 
('MySQL测试数据源', 'MYSQL', 'localhost', 3306, 'test_db', 'root', 'cm9vdA==', 1, 'system', '系统管理员'),
('Oracle测试数据源', 'ORACLE', 'localhost', 1521, 'orcl', 'hr', 'aHI=', 0, 'system', '系统管理员')
ON DUPLICATE KEY UPDATE name = name;


-- 字典分类表
CREATE TABLE `dea_dictionary` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `code` varchar(64) NOT NULL COMMENT '字典编码，唯一标识',
                                           `name` varchar(128) NOT NULL COMMENT '字典名称',
                                           `description` varchar(255) DEFAULT NULL COMMENT '字典描述',
                                           `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（1: 启用, 0: 停用）',
                                           `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=10004 DEFAULT CHARSET=utf8mb4 COMMENT='字典表';

-- 字典项表
CREATE TABLE `dea_dictionary_item` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                `dictionary_id` bigint(20) NOT NULL COMMENT '所属字典ID，外键关联字典表',
                                                `code` varchar(64) NOT NULL COMMENT '字典项编码，唯一标识',
                                                `name` varchar(128) NOT NULL COMMENT '字典项名称',
                                                `value` varchar(255) NOT NULL COMMENT '字典项值',
                                                `description` varchar(255) DEFAULT NULL COMMENT '字典项描述',
                                                `order_index` int(11) DEFAULT '0' COMMENT '排序字段，值越小越靠前',
                                                `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（1: 启用, 0: 停用）',
                                                `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `dictionary_id` (`dictionary_id`,`code`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COMMENT='字典项表';

-- 插入系统配置字典数据
INSERT INTO `dea_dictionary` (`code`, `name`, `description`, `status`) VALUES
('SYSTEM_CONFIG', '系统配置', '系统相关配置参数', 1)
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `description` = VALUES(`description`);

-- 插入数据库密码加密密钥配置
INSERT INTO `dea_dictionary_item` (`dictionary_id`, `code`, `name`, `value`, `description`, `order_index`, `status`) 
SELECT d.id, 'DATABASE_PASSWORD_KEY', '数据库密码加密密钥', '6fk6DrPeXKhRZHBdauEy3RPjc084xJRR', '用于数据库密码加密解密的AES密钥', 1, 1
FROM `dea_dictionary` d WHERE d.code = 'SYSTEM_CONFIG'
ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), `description` = VALUES(`description`);
