package com.chic.dea.domain.service.impl;

import com.chic.dea.apis.model.vo.DictionaryItemVO;
import com.chic.dea.domain.database.entity.Dictionary;
import com.chic.dea.domain.database.entity.DictionaryItem;
import com.chic.dea.domain.database.mapper.DictionaryItemMapper;
import com.chic.dea.domain.database.mapper.DictionaryMapper;
import com.chic.dea.domain.service.DictionaryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 字典服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DictionaryServiceImpl implements DictionaryService {

    private final DictionaryMapper dictionaryMapper;
    private final DictionaryItemMapper dictionaryItemMapper;

    @Override
    public List<DictionaryItemVO> getDictionaryItemsByCode(String dictionaryCode) {
        if (!StringUtils.hasText(dictionaryCode)) {
            throw new IllegalArgumentException("字典编码不能为空");
        }

        log.debug("根据字典编码查询字典项，编码: {}", dictionaryCode);

        // 先查询字典是否存在
        Dictionary dictionary = dictionaryMapper.findByCode(dictionaryCode);
        if (dictionary == null) {
            log.warn("字典编码不存在或已禁用: {}", dictionaryCode);
            return new ArrayList<>();
        }

        // 查询字典项列表
        List<DictionaryItem> dictionaryItems = dictionaryItemMapper.findByDictionaryCode(dictionaryCode);
        
        // 转换为VO
        List<DictionaryItemVO> result = new ArrayList<>();
        for (DictionaryItem item : dictionaryItems) {
            DictionaryItemVO vo = convertToVO(item, dictionary);
            result.add(vo);
        }

        log.debug("查询到字典项数量: {}", result.size());
        return result;
    }

    @Override
    public String getDictionaryItemValue(String dictionaryCode, String itemCode) {
        if (!StringUtils.hasText(dictionaryCode)) {
            throw new IllegalArgumentException("字典编码不能为空");
        }
        if (!StringUtils.hasText(itemCode)) {
            throw new IllegalArgumentException("字典项编码不能为空");
        }

        log.debug("根据字典编码和字典项编码查询字典项值，字典编码: {}, 字典项编码: {}", dictionaryCode, itemCode);

        String value = dictionaryItemMapper.findValueByDictionaryCodeAndItemCode(dictionaryCode, itemCode);
        
        if (value == null) {
            log.warn("未找到对应的字典项值，字典编码: {}, 字典项编码: {}", dictionaryCode, itemCode);
        }

        return value;
    }

    /**
     * 转换字典项实体为VO
     * 
     * @param item 字典项实体
     * @param dictionary 字典实体
     * @return 字典项VO
     */
    private DictionaryItemVO convertToVO(DictionaryItem item, Dictionary dictionary) {
        DictionaryItemVO vo = new DictionaryItemVO();
        vo.setId(item.getId());
        vo.setDictionaryId(item.getDictionaryId());
        vo.setDictionaryCode(dictionary.getCode());
        vo.setDictionaryName(dictionary.getName());
        vo.setCode(item.getCode());
        vo.setName(item.getName());
        vo.setValue(item.getValue());
        vo.setDescription(item.getDescription());
        vo.setOrderIndex(item.getOrderIndex());
        vo.setStatus(item.getStatus());
        vo.setStatusName(item.getStatus() == 1 ? "启用" : "禁用");
        vo.setCreatedTime(item.getCreatedTime());
        vo.setUpdatedTime(item.getUpdatedTime());
        return vo;
    }
}