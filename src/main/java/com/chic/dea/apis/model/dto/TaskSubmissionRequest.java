package com.chic.dea.apis.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Email;
import javax.validation.constraints.Pattern;

/**
 * 提数任务提交请求DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class TaskSubmissionRequest {

    /**
     * OA ID
     */
    @NotBlank(message = "OA ID不能为空")
    private String oaid;

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    private String title;

    /**
     * 申请人
     */
    @NotBlank(message = "申请人不能为空")
    private String applicant;

    /**
     * 申请人邮箱
     */
    @NotBlank(message = "申请人邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String applicantEmail;

    /**
     * 申请人手机
     */
    @NotBlank(message = "申请人手机不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String applicantPhone;

    /**
     * 提数人
     */
    @NotBlank(message = "提数人不能为空")
    private String extractor;

    /**
     * 提取脚本
     */
    @NotBlank(message = "提取脚本不能为空")
    private String extractionScript;

    /**
     * 数据源ID
     */
    @NotNull(message = "数据源ID不能为空")
    private Long dataSourceId;

    /**
     * 合规OA文件URL
     */
    private String compliancePdfUrl;

    /**
     * 是否需要合规审批
     */
    private Boolean requiresCompliance = false;

    /**
     * 是否已通过SQL校验
     */
    private Boolean sqlValidated = false;

    /**
     * 是否已确认预览数据
     */
    private Boolean previewConfirmed = false;
}