<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chic.dea.domain.database.mapper.OAInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.chic.dea.domain.database.entity.OAInfo">
        <id column="lcid" property="lcid" />
        <result column="bt" property="bt" />
        <result column="yx" property="yx" />
        <result column="sjhm" property="sjhm" />
        <result column="btqz" property="btqz" />
        <result column="sqrqz" property="sqrqz" />
        <result column="fqgsqz" property="fqgsqz" />
    </resultMap>

    <!-- 根据OA ID查询OA信息 -->
    <select id="findByLcid" resultMap="BaseResultMap">
        SELECT lcid, bt, yx, sjhm, btqz, sqrqz, fqgsqz 
        FROM ${tableName} 
        WHERE lcid = #{lcid}
    </select>

</mapper>
