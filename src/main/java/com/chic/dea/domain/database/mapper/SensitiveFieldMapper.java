package com.chic.dea.domain.database.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chic.dea.domain.database.entity.ColumnClassify;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 敏感字段Mapper接口
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Mapper
@DS("tianyu") // 指定使用OA数据源
public interface SensitiveFieldMapper extends BaseMapper<ColumnClassify> {

    /**
     * 根据表名和字段名查询敏感字段
     *
     * @param tableName 表名
     * @param fieldNames 多个字段名
     * @return 敏感字段
     */
    @Select("<script>" +
            "SELECT TABLE_NAME AS tableName, COLUMN_NAME AS columnName, SENSITIVE_FLAG AS sensitiveFlag " +
            "FROM DWS_METADATA_COLUMN_CLASSIFY " +
            "WHERE TABLE_NAME = #{tableName} " +
            "AND COLUMN_NAME IN " +
            "<foreach collection='fieldNames' item='fieldName' open='(' separator=',' close=')'>" +
            "#{fieldName}" +
            "</foreach> " +
            "AND IS_SENSITIVE_COLUMN = '1'" +
            "</script>")
    List<ColumnClassify> findSensitiveFields(@Param("tableName") String tableName, @Param("fieldNames") List<String> fieldNames);
}
