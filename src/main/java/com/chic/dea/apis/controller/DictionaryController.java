package com.chic.dea.apis.controller;

import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.dea.apis.model.vo.DictionaryItemVO;
import com.chic.dea.domain.service.DictionaryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 字典管理控制器
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@RestController
@RequestMapping("/api/dictionaries")
@RequiredArgsConstructor
public class DictionaryController {

    private final DictionaryService dictionaryService;

    /**
     * 根据字典编码查询字典项列表
     * 
     * @param code 字典编码
     * @return 字典项列表
     */
    @GetMapping("/{code}/items")
    public Result<List<DictionaryItemVO>> getDictionaryItemsByCode(@PathVariable String code) {
        try {
            List<DictionaryItemVO> dictionaryItems = dictionaryService.getDictionaryItemsByCode(code);
            log.info("根据字典编码查询字典项成功，编码: {}, 数量: {}", code, dictionaryItems.size());
            return Result.success(dictionaryItems);
        } catch (IllegalArgumentException e) {
            log.warn("根据字典编码查询字典项参数错误: {}", e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            log.error("根据字典编码查询字典项失败，编码: {}", code, e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "查询字典项失败: " + e.getMessage()));
        }
    }
}