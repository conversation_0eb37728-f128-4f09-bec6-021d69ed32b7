package com.chic.dea.apis.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 提数任务更新请求DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class TaskUpdateRequest {

    /**
     * 提取脚本
     */
    @NotBlank(message = "提取脚本不能为空")
    private String extractionScript;

    /**
     * 数据源ID
     */
    @NotNull(message = "数据源ID不能为空")
    private Long dataSourceId;

    /**
     * 合规OA文件URL
     */
    private String compliancePdfUrl;

    /**
     * 提数人
     */
    private String extractor;
}