package com.chic.dea.domain.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chic.dea.domain.database.entity.TaskExecutionLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务执行日志Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Mapper
public interface TaskExecutionLogMapper extends BaseMapper<TaskExecutionLog> {

    /**
     * 根据任务ID查询执行日志
     * 
     * @param taskId 任务ID
     * @return 执行日志列表
     */
    @Select("SELECT * FROM task_execution_log WHERE task_id = #{taskId} ORDER BY start_time DESC")
    List<TaskExecutionLog> findByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID和步骤名称查询日志
     * 
     * @param taskId 任务ID
     * @param stepName 步骤名称
     * @return 执行日志
     */
    @Select("SELECT * FROM task_execution_log WHERE task_id = #{taskId} AND step_name = #{stepName} ORDER BY start_time DESC LIMIT 1")
    TaskExecutionLog findByTaskIdAndStepName(@Param("taskId") Long taskId, @Param("stepName") String stepName);

    /**
     * 查询任务的最新日志
     * 
     * @param taskId 任务ID
     * @return 最新执行日志
     */
    @Select("SELECT * FROM task_execution_log WHERE task_id = #{taskId} ORDER BY start_time DESC LIMIT 1")
    TaskExecutionLog findLatestByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据状态查询日志
     * 
     * @param status 执行状态
     * @return 日志列表
     */
    @Select("SELECT * FROM task_execution_log WHERE status = #{status} ORDER BY start_time DESC")
    List<TaskExecutionLog> findByStatus(@Param("status") String status);

    /**
     * 查询失败的执行日志
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 失败日志列表
     */
    @Select("SELECT * FROM task_execution_log " +
            "WHERE status = 'FAILED' " +
            "AND start_time >= #{startTime} " +
            "AND start_time <= #{endTime} " +
            "ORDER BY start_time DESC")
    List<TaskExecutionLog> findFailedLogs(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 统计任务执行步骤的成功率
     * 
     * @param stepName 步骤名称
     * @param startTime 统计开始时间
     * @param endTime 统计结束时间
     * @return 统计结果
     */
    @Select("SELECT " +
            "COUNT(1) as total_count, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count, " +
            "AVG(CASE WHEN status = 'SUCCESS' AND end_time IS NOT NULL " +
            "    THEN TIMESTAMPDIFF(MICROSECOND, start_time, end_time) / 1000 " +
            "    ELSE NULL END) as avg_duration " +
            "FROM task_execution_log " +
            "WHERE step_name = #{stepName} " +
            "AND start_time >= #{startTime} " +
            "AND start_time <= #{endTime}")
    java.util.Map<String, Object> getStepStatistics(@Param("stepName") String stepName,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询执行时间最长的任务日志
     * 
     * @param limit 限制数量
     * @return 执行时间最长的日志列表
     */
    @Select("SELECT * FROM task_execution_log " +
            "WHERE status = 'SUCCESS' " +
            "AND end_time IS NOT NULL " +
            "ORDER BY TIMESTAMPDIFF(MICROSECOND, start_time, end_time) DESC " +
            "LIMIT #{limit}")
    List<TaskExecutionLog> findLongestExecutionLogs(@Param("limit") Integer limit);

    /**
     * 查询近期频繁失败的步骤
     * 
     * @param startTime 开始时间
     * @param failureThreshold 失败次数阈值
     * @return 频繁失败的步骤统计
     */
    @Select("SELECT step_name, COUNT(1) as failure_count " +
            "FROM task_execution_log " +
            "WHERE status = 'FAILED' " +
            "AND start_time >= #{startTime} " +
            "GROUP BY step_name " +
            "HAVING COUNT(1) >= #{failureThreshold} " +
            "ORDER BY failure_count DESC")
    List<java.util.Map<String, Object>> findFrequentFailureSteps(@Param("startTime") LocalDateTime startTime,
                                                                @Param("failureThreshold") Integer failureThreshold);

    /**
     * 清理历史日志（保留最近90天）
     * 
     * @param cutoffTime 截止时间
     */
    @Update("DELETE FROM task_execution_log WHERE create_at < #{cutoffTime}")
    void cleanupOldLogs(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计指定时间范围内的任务执行情况
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行统计
     */
    @Select("SELECT " +
            "step_name, " +
            "COUNT(1) as total_executions, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_executions, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_executions, " +
            "AVG(CASE WHEN status = 'SUCCESS' AND end_time IS NOT NULL " +
            "    THEN TIMESTAMPDIFF(MICROSECOND, start_time, end_time) / 1000 " +
            "    ELSE NULL END) as avg_duration_ms " +
            "FROM task_execution_log " +
            "WHERE start_time >= #{startTime} " +
            "AND start_time <= #{endTime} " +
            "GROUP BY step_name " +
            "ORDER BY total_executions DESC")
    List<java.util.Map<String, Object>> getExecutionStatistics(@Param("startTime") LocalDateTime startTime,
                                                              @Param("endTime") LocalDateTime endTime);
}