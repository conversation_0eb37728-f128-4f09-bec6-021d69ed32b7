package com.chic.dea.infrastructure.redis;

import com.chic.dea.infrastructure.general.constants.RedisKeyConstants;
import java.net.InetAddress;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;


/**
 * 分布式锁
 */
@Component
public class DistributedLockHandler {

    private static final Logger logger = LoggerFactory.getLogger(DistributedLockHandler.class);
    /** 单个业务持有锁的时间30s，防止死锁 */
    private final static long LOCK_EXPIRE = 30L;
    //默认100ms尝试一次
    private final static long LOCK_TRY_INTERVAL = 100L;
    //默认尝试20s
    private final static long LOCK_TRY_TIMEOUT = 20 * 1000L;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Value("#{lockRedisScript}")
    private DefaultRedisScript<Boolean> lockRedisScript;

    @Value("#{unlockRedisScript}")
    private DefaultRedisScript<Long> unlockRedisScript;

    /**
     * 尝试获取全局锁 获取锁失败不进行重试
     */
    public boolean tryLockNoReTry(RedisLock lock) {
        boolean result = getLock(lock, LOCK_EXPIRE);
        if (!result) {
            logger.info("\n 获取redis锁失败 lock.key:{} lock.value:{} " , lock.getName() ,lock.getValue());
        }
        return result;
    }

    /**
     * 尝试获取全局锁 使用默认的重试时间、过期时间
     *
     * @param lock 锁的名称
     * @return true 获取成功，false获取失败
     */
    public boolean tryLock(RedisLock lock) {
        return getLockRetry(lock, LOCK_TRY_TIMEOUT, LOCK_TRY_INTERVAL, LOCK_EXPIRE);
    }

    /**
     * 尝试获取全局锁
     *
     * @param lock    锁的名称
     * @param timeout 获取超时时间 单位ms
     * @return true 获取成功，false获取失败
     */
    public boolean tryLock(RedisLock lock, long timeout) {
        return getLockRetry(lock, timeout, LOCK_TRY_INTERVAL, LOCK_EXPIRE);
    }

    /**
     * 尝试获取全局锁
     *
     * @param lock        锁的名称
     * @param timeout     获取锁的超时时间
     * @param tryInterval 多少毫秒尝试获取一次
     * @return true 获取成功，false获取失败
     */
    public boolean tryLock(RedisLock lock, long timeout, long tryInterval) {
        return getLockRetry(lock, timeout, tryInterval, LOCK_EXPIRE);
    }

    /**
     * 尝试获取全局锁
     *
     * @param lock           锁的名称
     * @param timeout        获取锁的超时时间
     * @param tryInterval    多少毫秒尝试获取一次
     * @param lockExpireTime 锁的过期
     * @return true 获取成功，false获取失败
     */
    public boolean tryLock(RedisLock lock, long timeout, long tryInterval, long lockExpireTime) {
        return getLockRetry(lock, timeout, tryInterval, lockExpireTime);
    }


    /**
     * 操作redis获取全局锁
     *
     * @param lock           锁的名称
     * @param timeout        获取的超时时间
     * @param tryInterval    多少ms尝试一次
     * @param lockExpireTime 获取成功后锁的过期时间
     * @return true 获取成功，false获取失败
     */
    public boolean getLockRetry(RedisLock lock, long timeout, long tryInterval, long lockExpireTime) {
        try {
            if (StringUtils.isEmpty(lock.getName()) || StringUtils.isEmpty(lock.getValue())) {
                return false;
            }
            long startTime = System.currentTimeMillis();
            long continuedTime = 0L;
            do {
                if (getLock(lock,lockExpireTime)) {
                    return true;
                }
                continuedTime = System.currentTimeMillis() - startTime;
                Thread.sleep(tryInterval);
                //尝试超过了设定值之后直接跳出循环
            } while (continuedTime <= timeout);
        } catch (InterruptedException e) {
            logger.error(ExceptionUtils.getStackTrace(e));
            return false;
        }
        logger.info("\n 获取redis锁失败 lock.key:{} lock.value:{} " , lock.getName() ,lock.getValue());
        return false;
    }

    /**
     * 操作redis获取全局锁,不带等待及重试机制版
     *
     * @param lock           锁的名称
     * @param lockExpireTime 获取成功后锁的过期时间
     * @return true 获取成功，false获取失败
     */
    public boolean getLock(RedisLock lock, long lockExpireTime) {
        try {
            if (StringUtils.isEmpty(lock.getName()) || StringUtils.isEmpty(lock.getValue())) {
                return false;
            }
            long startTime = System.currentTimeMillis();
            long continuedTime = 0L;
            if (!redisTemplate.hasKey(lock.getName())) {
                boolean setFlag = redisTemplate.execute(lockRedisScript, Collections.singletonList(lock.getName()),Collections.singletonList(lock.getValue()), lockExpireTime, TimeUnit.MILLISECONDS);
                if (setFlag) {
                    continuedTime = System.currentTimeMillis() - startTime;
                    logger.info("\n ！！！获得redis锁 lock.key:{} lock.value:{} 耗时:{}" , lock.getName() , lock.getValue() , continuedTime);
                    return true;
                }
            }else {
                logger.debug("\n lock is exist lock.key:{} lock.value:{} " , lock.getName(),lock.getValue());
            }
        } catch (Exception e) {
            logger.error(ExceptionUtils.getStackTrace(e));
            return false;
        }
        return false;
    }

    /**
     * 释放锁
     */
    public boolean releaseLock(RedisLock lock) {
        if (!StringUtils.isEmpty(lock.getName())) {
            Long result = redisTemplate.execute(unlockRedisScript, Collections.singletonList(lock.getName()), Collections.singletonList(lock.getValue()));
            if (result != null && result.intValue() == 1) {
                logger.info("\n redis解锁 lock.key:{} lock.value:{} " , lock.getName(),lock.getValue());
                return true;
            }else {
                logger.error("\n redis释放锁失败 lock.key:{} lock.value:{} " , lock.getName(),lock.getValue());
            }
        }
        return false;
    }

    /**
     * 存在锁，释放锁
     * @param lock
     * @return
     */
    public boolean checkLock(RedisLock lock) {
        boolean bTrue = false;
        try {
            if (redisTemplate.hasKey(lock.getName())) {
                bTrue = true;
            }
        } catch (Exception e) {
            logger.error("check lock.key:{} lock.value:{} Error:{}", lock.getName(),lock.getValue(), ExceptionUtils.getStackTrace(e));
        } finally {
            if (bTrue) {
                releaseLock(lock);
            }
        }
        return bTrue;
    }

    public String getLockValue() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            String ip = addr.getHostAddress();
            return ip + RedisKeyConstants.REDIS_KEY_SEPARATOR + System.currentTimeMillis();

        } catch (Exception e) {
            logger.error("getLockValue Error:{}", ExceptionUtils.getStackTrace(e));
        }
        return System.currentTimeMillis() + "";
    }

    public RedisLock createLock(String lockKey, String lockValue) {
        return new RedisLock(lockKey, lockValue);
    }




}
